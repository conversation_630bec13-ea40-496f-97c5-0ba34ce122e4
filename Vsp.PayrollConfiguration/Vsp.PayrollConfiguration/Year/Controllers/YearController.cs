using Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Authorizations;
using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Authorizations;
using Vsp.PayrollConfiguration.Domain.Shared.Authorizations;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Domain.WageModel.Authorizations;
using Vsp.PayrollConfiguration.Domain.Year.Interfaces;
using Vsp.PayrollConfiguration.Domain.Year.Models;
using Vsp.PayrollConfiguration.Infrastructure.Attributes;
using Vsp.PayrollConfiguration.Infrastructure.Authorizations;
using Vsp.PayrollConfiguration.Infrastructure.Models;
using Vsp.PayrollConfiguration.Year.Constants;

namespace Vsp.PayrollConfiguration.Year.Controllers;

/// <summary>
/// Manage year (NL: Jaar)
/// </summary>
[Tags("Year")]
[Authorize]
[ApiController]
[Route("")]
public class YearController(IResultHandler resultHandler, IYearService service) : ControllerBase
{
    private readonly IResultHandler resultHandler = resultHandler;
    private readonly IYearService service = service;

    /// <summary>
    /// List of years (minimized)
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>GetYearsMinimizedByCollectiveLaborAgreementId</c>
    ///    - <c>GetYearsMinimizedByWageModelId</c>
    ///    - <c>GetYearsMinimizedByPayrollAdministrationId</c><br/>
    /// Retrieves a list of years (minimized) for a collective labor agreement (CLA), a wage model (WM) or a payroll administration (PA).
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(YearRoutes.GetYearsMinimizedByInheritanceLevelIdAsync)]
    [AuthorizeInheritanceLevelEntity<InheritanceLevelAuthorizationModel>(
        "389ff300-463b-4a73-9cef-4afda3a98e5e",  // GetYearsMinimizedByCollectiveLaborAgreementId
        "0c7070b2-ff66-4dbc-8d9e-fbb71f0ecad6",  // GetYearsMinimizedByWageModelId
        "ace468f2-bfa4-4a73-bb64-144509902e01")] // GetYearsMinimizedByPayrollAdministrationId
    public async Task<ActionResult<ListResult<YearMinimizedModel>>> GetYearsMinimizedByInheritanceLevelIdAsync([FromQuery][ExactlyOneIdRequired] InheritanceLevelQuerystringParams querystringParams)
    {
        var result = await this.service.GetYearsMinimizedByInheritanceLevelIdAsync(querystringParams.GetId());
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// List of years for a collective labor agreement
    /// </summary>
    /// <remarks>
    /// **Activity name**:
    ///    - <c>GetYearsByCollectiveLaborAgreementId</c><br/>
    /// Retrieves a list of years for a specific collective labor agreement (CLA).
    /// </remarks>
    /// <param name="collectiveLaborAgreementId" example="123e4567-e89b-12d3-a456-************">The unique identifier of a collective labor agreement (GUID/UUID).</param>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(YearRoutes.GetYearsByCollectionLaborAgreementIdAsync)]
    [AuthorizeEntity<CollectiveLaborAgreementAuthorizationModel>("9c57c043-417f-4560-b339-7c3440a4529b")] // GetYearsByCollectiveLaborAgreementId
    public async Task<ActionResult<ListResult<YearClaWmModel>>> GetYearsByCollectiveLaborAgreementIdAsync(Guid collectiveLaborAgreementId)
    {
        var result = await this.service.GetYearsByCollectiveLaborAgreementOrWageModelIdAsync(collectiveLaborAgreementId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// List of years for a wage model
    /// </summary>
    /// <remarks>
    /// **Activity name**:
    ///    - <c>GetYearsByWageModelId</c><br/>
    /// Retrieves a list of years for a specific wage model (WM).
    /// </remarks>
    /// <param name="wageModelId" example="123e4567-e89b-12d3-a456-************">The unique identifier of a wage model (GUID/UUID).</param>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(YearRoutes.GetYearsByWageModelIdAsync)]
    [AuthorizeEntity<WageModelAuthorizationModel>("c90c888a-e079-426e-9113-07788434950b")] // GetYearsByWageModelId
    public async Task<ActionResult<ListResult<YearClaWmModel>>> GetYearsByWageModelIdAsync(Guid wageModelId)
    {
        var result = await this.service.GetYearsByCollectiveLaborAgreementOrWageModelIdAsync(wageModelId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// List of years for a payroll administration
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>GetYearsByPayrollAdministrationId</c><br/>
    /// Retrieves a list of years for a payroll administration (PA).
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(YearRoutes.GetYearsByPayrollAdministrationIdAsync)]
    [AuthorizeEntity<PayrollAdministrationAuthorizationModel>("8cc6d25f-02ba-4c8a-bff7-282fcdf2b65a")] // GetYearsByPayrollAdministrationId
    public async Task<ActionResult<ListResult<YearPaModel>>> GetYearsByPayrollAdministrationIdAsync(Guid payrollAdministrationId)
    {
        var result = await this.service.GetYearsByPayrollAdministrationIdAsync(payrollAdministrationId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Edit a wage model year
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>PatchYearByWageModelYearId</c>
    /// Edit the details of an existing year.<br/>
    /// **Validation errors**:
    ///    - <c>API_PayrollConfiguration_Year_StandardShift_Invalid</c> standardShift.shiftNumber is invalid.
    ///    - <c>API_PayrollConfiguration_Year_StandardEmployeeProfile_Invalid</c> standardEmployeeProfile.employeeProfileNumber is invalid.
    /// </remarks>
    /// <param name="yearId" example="0000093c-07e4-0000-0000-000000000000">The unique identifier of a year (GUID/UUID).</param>
    /// <param name="patchModel">PATCH payload containing details of a year</param>
    [Consumes(MediaTypeNames.Application.Json)]
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Patch))]
    [HttpPatch]
    [Route(YearRoutes.PatchYearByWageModelYearIdAsync)]
    [AuthorizeInheritanceLevelEntity<YearAuthorizationModel>(
        null, // To block CLA level
        "ae608bae-35b8-47a6-8546-89caf4c2b1ef", // PatchYearByWageModelYearId
        null)] // To block PA level
    public async Task<ActionResult<DetailResult<YearClaWmModel>>> PatchYearByWageModelYearIdAsync(Guid yearId, [Required(ErrorMessage = "PATCH payload is required.")] YearWmPatchModel patchModel)
    {
        var result = await this.service.PatchYearByWageModelYearIdAsync(yearId, patchModel);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Edit a payroll administration year
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>PatchYearByPayrollAdministrationYearId</c><br/>
    /// Edit the details of an existing year.<br/>
    /// **Validation errors**:
    ///    - <c>API_PayrollConfiguration_Year_StandardShift_Invalid</c> standardShift.shiftNumber is invalid.
    ///    - <c>API_PayrollConfiguration_Year_StandardEmployeeProfile_Invalid</c> standardEmployeeProfile.employeeProfileNumber is invalid.
    ///    - <c>API_PayrollConfiguration_Year_TestYear_PreviousYearNotTest</c> testYear must be false if the previous year has testYear false.
    ///    - <c>API_PayrollConfiguration_Year_TestYear_PayrollTaxReturnPerformed</c> testYear must be false if a payroll tax return message has been sent this year.
    ///    - <c>API_PayrollConfiguration_Year_ZwSelfInsurerStartPayrollPeriod_Invalid</c> zwSelfInsurerStartPayrollPeriod is invalid.
    ///    - <c>API_PayrollConfiguration_Year_Aof_Invalid</c> aof.key is invalid.
    ///    - <c>API_PayrollConfiguration_Year_Aof_MustNotHaveValueBefore2022</c> aof must be unset for year &lt; 2022.
    ///    - <c>API_PayrollConfiguration_Year_Aof_MustHaveValueFrom2022</c> aof must be set for year >= 2022.
    ///    - <c>API_PayrollConfiguration_Year_DateAvailableEss_YearClosureNotRequestedAndYearNotClosed</c> dateAvailableEss may only be changed when a request for year closure has been sent, or the year has been closed.
    ///    - <c>API_PayrollConfiguration_Year_DateAvailableEss_OldDateAvailableEssNotEmptyAndNotInFuture</c> dateAvailableEss may only be changed when the current value is empty or in the future.
    ///    - <c>API_PayrollConfiguration_Year_DateAvailableEss_NewDateAvailableEssNotEmptyAndNotInFuture</c> dateAvailableEss must be set to a future date if not left empty.
    ///    - <c>API_PayrollConfiguration_Year_DateAvailableEss_NewDateAvailableEssEmptyAndYearClosed</c> dateAvailableEss may only be cleared when the year has not been closed yet.
    ///    - <c>API_PayrollConfiguration_Year_SendEssMail_YearClosureNotRequestedAndYearNotClosed</c> sendEssMail may only be changed when a request for year closure has been sent, or the year has been closed.
    ///    - <c>API_PayrollConfiguration_Year_SendEssMail_OldDateAvailableEssNotEmptyAndNotInFuture</c> sendEssMail may only be changed when the current dateAvailableEss is empty or in the future.
    /// </remarks>
    /// <param name="yearId" example="0000093d-07e4-0000-0000-000000000000">The unique identifier of a year (GUID/UUID).</param>
    /// <param name="patchModel">PATCH payload containing details of a year</param>
    [Consumes(MediaTypeNames.Application.Json)]
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Patch))]
    [HttpPatch]
    [Route(YearRoutes.PatchYearByPayrollAdministrationYearIdAsync)]
    [AuthorizeInheritanceLevelEntity<YearAuthorizationModel>(
        null, // To block CLA level
        null, // To block WM level 
        "b30ffe62-3c01-4a1e-ae98-b71a14259e15")] // PatchYearByPayrollAdministrationYearId
    public async Task<ActionResult<DetailResult<YearPaModel>>> PatchYearByPayrollAdministrationYearIdAsync(Guid yearId, [Required(ErrorMessage = "PATCH payload is required.")] YearPaPatchModel patchModel)
    {
        var result = await this.service.PatchYearByPayrollAdministrationYearIdAsync(yearId, patchModel);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Metadata for years at WM level
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>GetYearMetadataByWageModelYearId</c>
    /// Retrieves metadata for years at WM level.
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetDetail))]
    [HttpGet]
    [Route(YearRoutes.GetYearMetadataByWageModelYearIdAsync)]
    [AuthorizeInheritanceLevelEntity<YearAuthorizationModel>(
        null,  // To block CLA level
        "df7fb25b-ad0f-4725-b487-dc8b9d41def0",  // GetYearMetadataByWageModelYearId
        null)] // To block PA level
    public async Task<ActionResult<DetailResult<YearMetadataModel>>> GetYearMetadataByWageModelYearIdAsync(Guid yearId)
    {
        var result = await this.service.GetYearMetadataByYearIdAsync<YearMetadataModel>(yearId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Metadata for years at PA level
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>GetYearMetadataByPayrollAdministrationYearId</c>
    /// Retrieves metadata for years at PA level.
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetDetail))]
    [HttpGet]
    [Route(YearRoutes.GetYearMetadataByPayrollAdministrationYearIdAsync)]
    [AuthorizeInheritanceLevelEntity<YearAuthorizationModel>(
        null,  // To block CLA level
        null,  // To block WM level
        "2f11ed18-03a6-49e5-8b93-7d27a48b912b")] // GetYearMetadataByPayrollAdministrationYearId
    public async Task<ActionResult<DetailResult<YearMetadataPaModel>>> GetYearMetadataByPayrollAdministrationYearIdAsync(Guid yearId)
    {
        var result = await this.service.GetYearMetadataByYearIdAsync<YearMetadataPaModel>(yearId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// List of payroll periods for a year
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>GetPayrollPeriodsByCollectiveLaborAgreementYearId</c>
    ///    - <c>GetPayrollPeriodsByWageModelYearId</c>
    ///    - <c>GetPayrollPeriodsByPayrollAdministrationYearId</c><br/>
    /// Retrieves a list of payroll periods for a specific year.
    /// </remarks>
    /// <param name="yearId" example="123e4567-e89b-12d3-a456-************">The unique identifier of a year (GUID/UUID).</param>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(YearRoutes.GetPayrollPeriodsByYearIdAsync)]
    [AuthorizeInheritanceLevelEntity<YearAuthorizationModel>(
        "9193efcf-9d30-4b9b-be1e-31e0d4db83f6",  // GetPayrollPeriodsByCollectiveLaborAgreementYearId
        "ed0676ac-20cf-4057-a2c8-03d96e40fac5",  // GetPayrollPeriodsByWageModelYearId
        "f109926a-6ade-4489-a94c-6f23b134c660")] // GetPayrollPeriodsByPayrollAdministrationYearId
    public async Task<ActionResult<ListResult<PayrollPeriodModel>>> GetPayrollPeriodsByYearIdAsync(Guid yearId)
    {
        var result = await this.service.GetPayrollPeriodsByYearIdAsync(yearId);
        return this.resultHandler.ToTypedActionResult(result);
    }
}