using System.Net;
using Vsp.PayrollConfiguration.BaseForCalculationAgeBasedMinimum.Constants;
using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Authorizations;
using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Models;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Authorizations;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Interfaces;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Authorizations;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Attributes;
using Vsp.PayrollConfiguration.Infrastructure.Authorizations;
using Vsp.PayrollConfiguration.Infrastructure.Models;

namespace Vsp.PayrollConfiguration.BaseForCalculationAgeBasedMinimum.Controllers;

/// <summary>
/// Manage age-based minimums for a base for calculation (NL: grondslag minimums)
/// </summary>
[Tags("Base for Calculation Age Based Minimum")]
[Authorize]
[ApiController]
public class BaseForCalculationAgeBasedMinimumController(IResultHandler resultHandler, IBaseForCalculationAgeBasedMinimumService service) : ControllerBase
{
    private readonly IResultHandler resultHandler = resultHandler;
    private readonly IBaseForCalculationAgeBasedMinimumService service = service;

    /// <summary>
    /// List of age-based minimums
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///     - <c>GetBaseForCalculationAgeBasedMinimumsByCollectiveLaborAgreementBaseForCalculcationId</c>
    ///     - <c>GetBaseForCalculationAgeBasedMinimumsByWageModelBaseForCalculcationId</c>
    ///     - <c>GetBaseForCalculationAgeBasedMinimumsByPayrollAdministrationBaseForCalculcationId</c><br/>
    /// Retrieves a list of age-based minimums for a given base for calculation.
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(BaseForCalculationAgeBasedMinimumRoutes.GetBaseForCalculationAgeBasedMinimumsByBaseForCalculationIdAsync)]
    [AuthorizeInheritanceLevelEntity<BaseForCalculationAuthorizationModel>(
        "d0e44ca4-2d34-4758-bf3a-2fae844ec65e", // GetBaseForCalculationAgeBasedMinimumsByCollectiveLaborAgreementBaseForCalculcationId
        "dbd27439-108c-4879-9b02-7362cc60c450", // GetBaseForCalculationAgeBasedMinimumsByWageModelBaseForCalculcationId
        "2346218d-597d-414c-869f-0b6e1c0264e9")] // GetBaseForCalculationAgeBasedMinimumsByPayrollAdministrationBaseForCalculcationId
    public async Task<ActionResult<ListResult<BaseForCalculationAgeBasedMinimumModel>>> GetBaseForCalculationAgeBasedMinimumsByBaseForCalculationIdAsync([FromRoute] Guid baseForCalculationId)
    {
        var result = await this.service.GetBaseForCalculationAgeBasedMinimumsByBaseForCalculationIdAsync(baseForCalculationId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Add age-based minimum
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///     - <c>PostBaseForCalculationAgeBasedMinimumByCollectiveLaborAgreementBaseForCalculationId</c>
    ///     - <c>PostBaseForCalculationAgeBasedMinimumByWageModelBaseForCalculationId</c>
    ///     - <c>PostBaseForCalculationAgeBasedMinimumByPayrollAdministrationBaseForCalculationId</c><br/>
    /// Add a new age-based minimum to a base for calculation.
    /// </remarks>
    [Consumes(MediaTypeNames.Application.Json)]
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Post))]
    [HttpPost]
    [Route(BaseForCalculationAgeBasedMinimumRoutes.PostBaseForCalculationAgeBasedMinimumByBaseForCalculationIdAsync)]
    [AuthorizeInheritanceLevelEntity<BaseForCalculationAuthorizationModel>(
        "450cdba7-db0d-4224-8113-d6a07f6dcf5b", // PostBaseForCalculationAgeBasedMinimumByCollectiveLaborAgreementBaseForCalculationId
        "1bec4d69-2573-4dca-b1fe-d4a4692cc066", // PostBaseForCalculationAgeBasedMinimumByWageModelBaseForCalculationId
        "d19240e9-04e1-4bd7-a0e5-a6adcb599998")] // PostBaseForCalculationAgeBasedMinimumByPayrollAdministrationBaseForCalculationId
    public async Task<ActionResult<DetailResult<BaseForCalculationAgeBasedMinimumModel>>> PostBaseForCalculationAgeBasedMinimumByBaseForCalculationIdAsync(
        [FromRoute] Guid baseForCalculationId,
        [FromBody]
        [Required(ErrorMessage = "POST payload is required.")] BaseForCalculationAgeBasedMinimumPostModel postModel)
    {
        var result = await this.service.PostBaseForCalculationAgeBasedMinimumByBaseForCalculationIdAsync(baseForCalculationId, postModel);
        return this.resultHandler.ToTypedActionResult(result, (int)HttpStatusCode.Created);
    }

    /// <summary>
    /// Edit an age-based minimum
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///     - <c>PatchBaseForCalculationAgeBasedMinimumByCollectiveLaborAgreementBaseForCalculationAgeBasedMinimumId</c>
    ///     - <c>PatchBaseForCalculationAgeBasedMinimumByWageModelBaseForCalculationAgeBasedMinimumId</c>
    ///     - <c>PatchBaseForCalculationAgeBasedMinimumByPayrollAdministrationBaseForCalculationAgeBasedMinimumId</c><br/>
    /// Edit the details of an age-based minimum for a base for calculation.
    /// </remarks>
    /// <param name="baseForCalculationAgeBasedMinimumId" example="cc14f81a-5d4e-4f2f-a09b-6254cb4bd129">The unique identifier of an age-based minimum (GUID/UUID).</param>
    /// <param name="patchModel">PATCH payload containing details of an age-based minimum</param>
    [Consumes(MediaTypeNames.Application.Json)]
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Patch))]
    [HttpPatch]
    [Route(BaseForCalculationAgeBasedMinimumRoutes.PatchBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync)]
    [AuthorizeInheritanceLevelEntity<BaseForCalculationAgeBasedMinimumAuthorizationModel>(
        "fb22e09e-b2a2-4850-9912-bf88ee18a974", // PatchBaseForCalculationAgeBasedMinimumByCollectiveLaborAgreementBaseForCalculationAgeBasedMinimumId
        "3518ba0e-c099-415d-a200-323860d92f6d", // PatchBaseForCalculationAgeBasedMinimumByWageModelBaseForCalculationAgeBasedMinimumId
        "a0d5419a-da1b-457f-af9c-3e4c6729ea53")] // PatchBaseForCalculationAgeBasedMinimumByPayrollAdministrationBaseForCalculationAgeBasedMinimumId
    public async Task<ActionResult<DetailResult<BaseForCalculationAgeBasedMinimumModel>>> PatchBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync(
        [FromRoute] Guid baseForCalculationAgeBasedMinimumId,
        [Required(ErrorMessage = "PATCH payload is required.")] BaseForCalculationAgeBasedMinimumPatchModel patchModel)
    {
        var result = await this.service.PatchBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync(baseForCalculationAgeBasedMinimumId, patchModel);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Delete an age-based minimum
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///     - <c>DeleteBaseForCalculationAgeBasedMinimumByCollectiveLaborAgreementBaseForCalculationAgeBasedMinimumId</c>
    ///     - <c>DeleteBaseForCalculationAgeBasedMinimumByWageModelBaseForCalculationAgeBasedMinimumId</c>
    ///     - <c>DeleteBaseForCalculationAgeBasedMinimumByPayrollAdministrationBaseForCalculationAgeBasedMinimumId</c><br/>
    /// Delete an existing age-based minimum.
    /// </remarks>
    /// <param name="baseForCalculationAgeBasedMinimumId" example="cc14f81a-5d4e-4f2f-a09b-6254cb4bd129">The unique identifier of an age-based minimum (GUID/UUID).</param>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Delete))]
    [HttpDelete]
    [Route(BaseForCalculationAgeBasedMinimumRoutes.DeleteBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync)]
    [AuthorizeInheritanceLevelEntity<BaseForCalculationAgeBasedMinimumAuthorizationModel>(
        "8058bfe0-1e86-48c8-bf3d-537e683016f5", // DeleteBaseForCalculationAgeBasedMinimumByCollectiveLaborAgreementBaseForCalculationAgeBasedMinimumId
        "7bb31fe3-e437-45a2-9b9f-94480c8e5f3e", // DeleteBaseForCalculationAgeBasedMinimumByWageModelBaseForCalculationAgeBasedMinimumId
        "1dc62a64-aeb8-4b80-b42d-a16efc838476")] // DeleteBaseForCalculationAgeBasedMinimumByPayrollAdministrationBaseForCalculationAgeBasedMinimumId
    public async Task<ActionResult<DetailResult<NoResult>>> DeleteBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync([FromRoute] Guid baseForCalculationAgeBasedMinimumId)
    {
        var result = await this.service.DeleteBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync(baseForCalculationAgeBasedMinimumId);
        return this.resultHandler.ToTypedActionResult(result);
    }
}