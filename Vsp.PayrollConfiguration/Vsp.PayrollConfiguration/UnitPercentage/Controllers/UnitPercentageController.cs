using Vsp.PayrollConfiguration.Domain.Shared.Authorizations;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Authorizations;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Interfaces;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Models;
using Vsp.PayrollConfiguration.Infrastructure.Attributes;
using Vsp.PayrollConfiguration.Infrastructure.Authorizations;
using Vsp.PayrollConfiguration.Infrastructure.Models;
using Vsp.PayrollConfiguration.UnitPercentage.Constants;

namespace Vsp.PayrollConfiguration.UnitPercentage.Controllers;

/// <summary>
/// Manage unit percentages (NL: eenheid percentage)
/// </summary>
[Tags("UnitPercentage")]
[Authorize]
[ApiController]
[Route("")]
public class UnitPercentageController(IResultHandler resultHandler, IUnitPercentageService service) : ControllerBase
{
    private readonly IResultHandler resultHandler = resultHandler;
    private readonly IUnitPercentageService service = service;

    /// <summary>
    /// List of unit percentages
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>GetUnitPercentagesByCollectiveLaborAgreementYearId</c>
    ///    - <c>GetUnitPercentagesByWageModelYearId</c>
    ///    - <c>GetUnitPercentagesByPayrollAdministrationYearId</c><br/>
    /// Retrieves a list of unit percentages for the given year.
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(UnitPercentageRoutes.GetUnitPercentagesByYearIdAsync)]
    [AuthorizeInheritanceLevelEntity<YearAuthorizationModel>(
        "6d67116f-28b9-48fd-a2c1-c8bb45363b32",  // GetUnitPercentagesByCollectiveLaborAgreementYearId
        "0202c028-79a4-4ea3-84f2-cf52184e553f",  // GetUnitPercentagesByWageModelYearId
        "e0c44931-7176-418d-a737-c4ce138d755b")] // GetUnitPercentagesByPayrollAdministrationYearId
    public async Task<ActionResult<ListResult<UnitPercentageModel>>> GetUnitPercentagesByYearAsync([FromRoute] Guid yearId)
    {
        var result = await this.service.GetUnitPercentagesByYearIdAsync(yearId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Add a unit percentage
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>PostUnitPercentageByCollectiveLaborAgreementId</c>
    ///    - <c>PostUnitPercentageByWageModelId</c>
    ///    - <c>PostUnitPercentageByPayrollAdministrationId</c><br/>
    /// Add a unit percentage for a collective labor agreement (CLA), a wage model (WM) or a payroll administration (PA).
    /// <br/>**Validation errors**:
    ///    - <c>API_PayrollConfiguration_Insert_PayrollPeriod_DoesNotExist</c> Payroll period does not exist on current inheritance level.
    ///    - <c>API_PayrollConfiguration_Insert_PayrollPeriod_FirstPeriodDoesNotExist</c> Cannot add data for a later payroll period in this year, because there is no data for the first payroll period yet.
    ///    - <c>API_PayrollConfiguration_Insert_Entity_AlreadyExists_CurrentInheritanceLevel</c> Entity already exists on current inheritance level.
    ///    - <c>API_PayrollConfiguration_Insert_Entity_AlreadyExists_ParentInheritanceLevel</c> Entity already exists on parent inheritance level.
    ///    - <c>API_PayrollConfiguration_UnitPercentage_PayrollComponent_Invalid</c> payrollComponent.key is invalid for unit percentage: it doesn't exist on current inheritance level or doesn't have category 6 (unit).
    ///    - <c>API_PayrollConfiguration_UnitPercentage_CalculateOver_Invalid</c> calculateOver.key is invalid.
    /// <br/>**Validation warnings**:
    ///    - <c>API_PayrollConfiguration_Insert_Entity_NotAddedToFutureYear</c> Failed to automatically add entity to future year(s) as well. See properties for details.
    /// </remarks>
    /// <param name="querystringParams">Querystring parameters.</param>
    /// <param name="postModel">POST payload containing details of a unit percentage.</param>
    [Consumes(MediaTypeNames.Application.Json)]
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Post))]
    [HttpPost]
    [Route(UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync)]
    [AuthorizeInheritanceLevelEntity<InheritanceLevelAuthorizationModel>(
        "5a8fdd58-91d1-42e5-b28a-270c2137c519",  // PostUnitPercentageByCollectiveLaborAgreementId
        "3638e1ac-f8c4-45f9-b286-e415d4213f26",  // PostUnitPercentageByWageModelId
        "414e6cd9-27cd-4c51-b682-26ce55f5b807")] // PostUnitPercentageByPayrollAdministrationId
    public async Task<ActionResult<DetailResult<UnitPercentageModel>>> PostUnitPercentageByInheritanceLevelIdAsync([FromQuery][ExactlyOneIdRequired] InheritanceLevelQuerystringParams querystringParams, [Required(ErrorMessage = "POST payload is required.")] UnitPercentagePostModel postModel)
    {
        var result = await this.service.PostUnitPercentageByInheritanceLevelIdAsync(querystringParams.GetId(), postModel);
        return this.resultHandler.ToTypedActionResult(result, StatusCodes.Status201Created);
    }

    /// <summary>
    /// Edit a unit percentage
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>PatchUnitPercentageByCollectiveLaborAgreementUnitPercentageId</c>
    ///    - <c>PatchUnitPercentageByWageModelUnitPercentageId</c>
    ///    - <c>PatchUnitPercentageByPayrollAdministrationUnitPercentageId</c><br/>
    /// Edit the details of an existing unit percentage.<br/>
    /// **Validation errors**:
    ///    - <c>API_PayrollConfiguration_UnitPercentage_CalculateOver_Invalid</c> calculateOver.key is invalid.
    /// </remarks>
    /// <param name="unitPercentageId" example="123e4567-e89b-12d3-a456-************">The unique identifier of a unit percentage (GUID/UUID).</param>
    /// <param name="patchModel">PATCH payload containing details of a unit percentage</param>
    [Consumes(MediaTypeNames.Application.Json)]
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Patch))]
    [HttpPatch]
    [Route(UnitPercentageRoutes.PatchUnitPercentageByUnitPercentageIdAsync)]
    [AuthorizeInheritanceLevelEntity<UnitPercentageAuthorizationModel>(
        "e8f4bef4-70a6-4c57-b354-49de1dabd452",  // PatchUnitPercentageByCollectiveLaborAgreementUnitPercentageId
        "5908f461-c341-4a5b-afb3-ad1d18fbb911",  // PatchUnitPercentageByWageModelUnitPercentageId
        "a0638369-a07e-4787-a418-66bcc7088f73")] // PatchUnitPercentageByPayrollAdministrationUnitPercentageId
    public async Task<ActionResult<DetailResult<UnitPercentageModel>>> PatchUnitPercentageByUnitPercentageIdAsync(Guid unitPercentageId, [Required(ErrorMessage = "PATCH payload is required.")] UnitPercentagePatchModel patchModel)
    {
        var result = await this.service.PatchUnitPercentageByUnitPercentageIdAsync(unitPercentageId, patchModel);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Delete a unit percentage
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>DeleteUnitPercentageByCollectiveLaborAgreementUnitPercentageId</c>
    ///    - <c>DeleteUnitPercentageByWageModelUnitPercentageId</c>
    ///    - <c>DeleteUnitPercentageByPayrollAdministrationUnitPercentageId</c><br/>
    /// Delete an existing unit percentage.<br/>
    /// **Validation errors**:
    ///    - <c>API_PayrollConfiguration_Delete_EntityHasChildren</c> Cannot delete entities from current inheritance level, because it has dependent child inheritance level(s).
    ///    - <c>API_PayrollConfiguration_Delete_PayrollPeriod_FirstPeriodCannotBeDeleted</c> Cannot delete data for the first payroll period in this year, because there is still data for later payroll period(s).
    /// </remarks>
    /// <param name="unitPercentageId" example="123e4567-e89b-12d3-a456-************">The unique identifier of a unit percentage (GUID/UUID).</param>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Delete))]
    [HttpDelete]
    [Route(UnitPercentageRoutes.DeleteUnitPercentageByUnitPercentageIdAsync)]
    [AuthorizeInheritanceLevelEntity<UnitPercentageAuthorizationModel>(
        "dc4b117b-ebcf-400b-a3d1-dc98a263090c",  // DeleteUnitPercentageByCollectiveLaborAgreementUnitPercentageId
        "040033bb-ef7b-4633-a0da-796c9283c203",  // DeleteUnitPercentageByWageModelUnitPercentageId
        "282d432a-f795-4cd2-8d39-0fa4ed6bf253")] // DeleteUnitPercentageByPayrollAdministrationUnitPercentageId
    public async Task<ActionResult<DetailResult<NoResult>>> DeleteUnitPercentageByUnitPercentageIdAsync(Guid unitPercentageId)
    {
        var result = await this.service.DeleteUnitPercentageByUnitPercentageIdAsync(unitPercentageId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Metadata for unit percentages per year
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>GetUnitPercentageMetadataByCollectiveLaborAgreementYearId</c>
    ///    - <c>GetUnitPercentageMetadataByWageModelYearId</c>
    ///    - <c>GetUnitPercentageMetadataByPayrollAdministrationYearId</c>
    /// Retrieves unit percentage metadata for the given year.
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetDetail))]
    [HttpGet]
    [Route(UnitPercentageRoutes.GetUnitPercentageMetadataByYearIdAsync)]
    [AuthorizeInheritanceLevelEntity<YearAuthorizationModel>(
        "b9b2486a-1b71-4b67-bc31-28540539d969",  // GetUnitPercentageMetadataByCollectiveLaborAgreementYearId
        "b202f797-70bd-44cb-a3f4-210535d03dd0",  // GetUnitPercentageMetadataByWageModelYearId
        "a52e5c94-0d60-4629-9282-82b4ae9dec2a")] // GetUnitPercentageMetadataByPayrollAdministrationYearId
    public async Task<ActionResult<DetailResult<UnitPercentageMetadataModel>>> GetUnitPercentageMetadataByYearIdAsync([FromRoute] Guid yearId)
    {
        var result = await this.service.GetUnitPercentageMetadataByYearIdAsync(yearId);
        return this.resultHandler.ToTypedActionResult(result);
    }
}