namespace Vsp.PayrollConfiguration.Repository.Entities;

public class ModelBaseForCalculationBasePayrollComponent : GeneratedIdEntity, IInheritanceEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int BaseForCalculationId { get; set; }
    [GeneratedIdKey<short>(3)]
    public int ComponentId { get; set; }
    [GeneratedIdKey<short>(4)]
    public int PayrollPeriodId { get; set; }

    public int? Origin { get; set; }

    public InheritanceLevel InheritanceLevel { get; set; } = null!;
}