namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class ShiftConfiguration : GeneratedIdEntityTypeConfigurationBase<Shift>
{
    public override void Configure(EntityTypeBuilder<Shift> builder)
    {
        base.Configure(builder);

        builder.ToTable("<PERSON>loeg", "Ulsa");
        builder.HasKey(x => new { x.InheritanceLevelId, x.YearId, x.ShiftId, x.PayrollPeriodId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.ShiftId).HasColumnName("PloegID");
        builder.Property(x => x.PayrollPeriodId).HasColumnName("VerloningsPeriodeID");

        builder.Property(x => x.ShiftIdDefinedAtLevel).HasColumnName("PloegIDAttribuut");
        builder.Property(x => x.FullTimeHoursPerWeek).HasColumnName("Uren").HasPrecision(5, 2);
        builder.Property(x => x.FullTimeHoursPerWeekDefinedAtLevel).HasColumnName("UrenAttribuut");
        builder.Property(x => x.BonusPercentage).HasColumnName("Percentage").HasPrecision(6, 3);
        builder.Property(x => x.BonusPercentageDefinedAtLevel).HasColumnName("PercentageAttribuut");

        builder.HasOne(x => x.InheritanceLevel).WithMany().HasForeignKey(x => x.InheritanceLevelId);
        builder.HasOne(x => x.Year).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId });
        builder.HasOne(x => x.PayrollPeriod).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.PayrollPeriodId });
    }
}
