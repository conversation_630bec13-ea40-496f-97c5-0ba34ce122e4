namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class FondsGrafischConfiguration : GeneratedIdEntityTypeConfigurationBase<FondsGrafisch>
{
    public override void Configure(EntityTypeBuilder<FondsGrafisch> builder)
    {
        base.Configure(builder);

        builder.ToTable("FondsGrafisch", "Ulsa");
        builder.<PERSON><PERSON>(x => new { x.WerkgeverID, x.Jaar<PERSON>, x.FondsGrafischID, x.VerloningsPeriodeID });
    }
}
