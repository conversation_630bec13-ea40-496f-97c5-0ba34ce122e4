namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class ModelZiektekostenConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelZiektekosten>
{
    public override void Configure(EntityTypeBuilder<ModelZiektekosten> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelZiektekosten", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON><PERSON>(x => new { x.WerkgeverID, x.<PERSON>, x.ZiekteKostenID, x.VerloningsPeriodeID });
    }
}
