namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class ModelFondsConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelFonds>
{
    public override void Configure(EntityTypeBuilder<ModelFonds> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelFonds", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON>(x => new { x.WerkgeverID, x.<PERSON>, x.FondsID, x.VerloningsPeriodeID });
    }
}
