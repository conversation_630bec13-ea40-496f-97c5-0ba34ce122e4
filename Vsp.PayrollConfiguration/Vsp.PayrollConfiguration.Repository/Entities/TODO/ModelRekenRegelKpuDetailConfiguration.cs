namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class ModelRekenRegelKpuDetailConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelRekenRegelKpuDetail>
{
    public override void Configure(EntityTypeBuilder<ModelRekenRegelKpuDetail> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelRekenRegelKpuDetail", "Ulsa", x => x.<PERSON>("trigger"));
        builder.Has<PERSON>ey(x => new { x.WerkgeverID, x.<PERSON>, x.<PERSON>KpuID, x.<PERSON>DetailID });
    }
}
