namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class ModelGrondslagMaximumConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelGrondslagMaximum>
{
    public override void Configure(EntityTypeBuilder<ModelGrondslagMaximum> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelGrondslagMaximum", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON><PERSON>(x => new { x.WerkgeverID, x.JaarID, x.GrondslagID, x.GrondslagMaximumID, x.VerloningsPeriodeID });
    }
}
