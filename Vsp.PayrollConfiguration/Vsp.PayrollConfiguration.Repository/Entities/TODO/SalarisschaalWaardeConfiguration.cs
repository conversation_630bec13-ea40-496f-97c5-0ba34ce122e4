namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class SalarisschaalWaardeConfiguration : GeneratedIdEntityTypeConfigurationBase<SalarisschaalWaarde>
{
    public override void Configure(EntityTypeBuilder<SalarisschaalWaarde> builder)
    {
        base.Configure(builder);

        builder.ToTable("SalarisschaalWaarde", "Ulsa");
        builder.<PERSON><PERSON>(x => new { x.WerkgeverID, x.SalarisschaalID, x.<PERSON>arisschaalCelID, x.SalarisschaalWaardeID });
    }
}
