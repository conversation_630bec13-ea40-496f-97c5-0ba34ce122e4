namespace Vsp.PayrollConfiguration.Repository.Entities.CodeTable;

/// <remarks>
/// We need this type configuration to map <c>UlsaCode</c> to <c>Code</c>.
/// </remarks>
internal class CtPayslipTypeConfiguration : CodeTableEntityTypeConfiguration<CtPayslipType>
{
    public override void Configure(EntityTypeBuilder<CtPayslipType> builder)
    {
        base.Configure(builder);
        builder.ToTable("ctStrook", "dbo");

        builder.Property(x => x.Code).HasColumnName("UlsaCode");
    }
}