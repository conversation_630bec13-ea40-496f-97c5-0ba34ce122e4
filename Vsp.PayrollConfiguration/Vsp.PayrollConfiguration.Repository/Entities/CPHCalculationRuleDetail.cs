namespace Vsp.PayrollConfiguration.Repository.Entities;

public class CPHCalculationRuleDetail : GeneratedIdEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int CPHCalculationRuleId { get; set; }
    [GeneratedIdKey<short>(3)]
    public int CPHCalculationRuleDetailId { get; set; }

    public int ComponentId { get; set; }

    public ModelComponent? ModelComponent { get; set; }
}