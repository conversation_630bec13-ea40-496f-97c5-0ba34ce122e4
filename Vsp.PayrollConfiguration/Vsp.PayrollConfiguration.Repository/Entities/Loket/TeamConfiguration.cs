namespace Vsp.PayrollConfiguration.Repository.Entities.Loket;

internal class TeamConfiguration : IdEntityTypeConfigurationBase<Team>
{
    public override void Configure(EntityTypeBuilder<Team> builder)
    {
        base.Configure(builder);

        builder.ToTable("Team", "Loket");
        builder.<PERSON>Key(x => new { x.ProviderId, x.TeamId });

        builder.Property(x => x.ProviderId).HasColumnName("AdministratieKantoorID");
        builder.Property(x => x.TeamId).HasColumnName("TeamID");
    }
}
