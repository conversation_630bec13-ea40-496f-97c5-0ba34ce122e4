namespace Vsp.PayrollConfiguration.Repository.Entities;

public class ModelComponent : GeneratedIdEntity, IInheritanceEntity, IYearEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int ComponentId { get; set; }

    public string? Description { get; set; }
    public int? DeductionOrPayment { get; set; }
    public int? PaymentPeriod { get; set; }
    public int? TaxLiable { get; set; }
    public int? SocialSecurityLiable { get; set; }
    public int? HoursIndication { get; set; }
    public int? CostsEmployer { get; set; }
    public int? IsNetToGross { get; set; }
    public int? IsFullTime { get; set; }
    public int? IsBaseForCalculationOvertime { get; set; }
    public int? IsTravelExpense { get; set; }
    public int? SuppressPrinting { get; set; }
    public int? SuppressPrintingAccumulations { get; set; }
    public int? IsBaseForCalculationDailyWageZw { get; set; }
    public int? IsBaseForCalculationDailyWageSupplement { get; set; }
    public int? BaseForCalculationBter { get; set; }
    public int? IsPayment { get; set; }
    public string? PaymentDescription { get; set; }
    public int? IsOvertime { get; set; }
    public int? Column { get; set; }
    public int? BalanceSheetSide { get; set; }
    public int? Category { get; set; }

    // Properties not in API but in DB
    public int? GeneralLedgerAccountNumber { get; set; }
    public int? IsLifeSpanScheme { get; set; }
    public int? Order { get; set; }
    public int? IsVisibleByDefault { get; set; }

    public InheritanceLevel InheritanceLevel { get; set; } = null!;

    public CtDeductionOrPayment? CtDeductionOrPayment { get; set; }
    public CtPaymentPeriod? CtPaymentPeriod { get; set; }
    public CtTaxLiable? CtTaxLiable { get; set; }
    public CtSocialSecurityLiable? CtSocialSecurityLiable { get; set; }
    public CtHoursIndication? CtHoursIndication { get; set; }
    public CtCostsEmployer? CtCostsEmployer { get; set; }
    public CtYesNo? CtIsNetToGross { get; set; }
    public CtYesNo? CtIsFullTime { get; set; }
    public CtYesNo? CtIsBaseForCalculationOvertime { get; set; }
    public CtYesNo? CtIsTravelExpense { get; set; }
    public CtYesNo? CtSuppressPrinting { get; set; }
    public CtYesNo? CtSuppressPrintingAccumulations { get; set; }
    public CtYesNo? CtIsBaseForCalculationDailyWageZw { get; set; }
    public CtYesNo? CtIsBaseForCalculationDailyWageSupplement { get; set; }
    public CtBaseForCalculationBter? CtBaseForCalculationBter { get; set; }
    public CtYesNo? CtIsPayment { get; set; }
    public CtYesNo? CtIsOvertime { get; set; }
    public CtColumn? CtColumn { get; set; }
    public CtBalanceSheetSide? CtBalanceSheetSide { get; set; }
    public CtCategory? CtCategory { get; set; }
    public CtYesNo? CtIsLifeSpanScheme { get; set; }
    public CtYesNo? CtIsVisibleByDefault { get; set; }

    public ComponentGeneralLedgerAccount? ComponentLedgerAccount { get; set; }
    public ComponentExemptFromAttachmentOfEarnings? ComponentWageGarnishmentExemptCompensation { get; set; }
}