namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class LedgerAccountToPayrollComponentLinkConfiguration : GeneratedIdEntityTypeConfigurationBase<LedgerAccountToPayrollComponentLink>
{
    public override void Configure(EntityTypeBuilder<LedgerAccountToPayrollComponentLink> builder)
    {
        base.Configure(builder);

        builder.ToView("VerdelingSchema", "Ulsa");
        builder.HasNo<PERSON>ey();

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.JournalProfileId).HasColumnName("RekeningSchemaID");
        builder.Property(x => x.LedgerAccountToPayrollComponentLinkId).HasColumnName("VerdelingSchemaID");
        builder.Property(x => x.ComponentId).HasColumnName("ComponentID");
    }
}
