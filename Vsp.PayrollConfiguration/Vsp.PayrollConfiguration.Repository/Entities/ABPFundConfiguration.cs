namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class ABPFundConfiguration : GeneratedIdEntityTypeConfigurationBase<ABPFund>
{
    public override void Configure(EntityTypeBuilder<ABPFund> builder)
    {
        base.Configure(builder);

        builder.ToTable("FondsABP", "Ulsa");
        builder.HasKey(x => new { x.InheritanceLevelId, x.YearId, FundABPId = x.ABPFundId, x.PayrollPeriodId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.ABPFundId).HasColumnName("FondsABPID");
        builder.Property(x => x.PayrollPeriodId).HasColumnName("VerloningsPeriodeID");
    }
}