namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class ArrangementComponentConfiguration : GeneratedIdEntityTypeConfigurationBase<ArrangementComponent>
{
    public override void Configure(EntityTypeBuilder<ArrangementComponent> builder)
    {
        base.Configure(builder);

        builder.ToTable("RegelingComponent", "Ulsa");
        builder.<PERSON><PERSON><PERSON>(x => new { x.ArrangementType, x.Identification, x.ComponentId });

        builder.Property(x => x.ArrangementType).HasColumnName("SoortRegeling");
        builder.Property(x => x.Identification).HasColumnName("Identificatie");
        builder.Property(x => x.ComponentId).HasColumnName("ComponentID");
        builder.Property(x => x.ComponentType).HasColumnName("SoortComponent");

        builder.HasOne(x => x.CtArrangementType).WithMany().HasForeignKey(x => x.ArrangementType);
    }
}
