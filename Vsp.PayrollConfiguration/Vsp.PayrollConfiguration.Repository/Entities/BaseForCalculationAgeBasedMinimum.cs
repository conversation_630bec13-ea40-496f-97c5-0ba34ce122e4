namespace Vsp.PayrollConfiguration.Repository.Entities;

public class BaseForCalculationAgeBasedMinimum : GeneratedIdEntity, IInheritanceEntity, IPayrollPeriodEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int BaseForCalculationId { get; set; }
    [GeneratedIdKey<short>(3)]
    public int Age { get; set; }
    [GeneratedIdKey<short>(4)]
    public int PayrollPeriodId { get; set; }

    public decimal Minimum { get; set; }

    // DefinedAtLevel properties
    public int AgeDefinedAtLevel { get; set; }
    public int PayrollPeriodDefinedAtLevel { get; set; }
    public int MinimumDefinedAtLevel { get; set; }

    // Navigation properties
    public InheritanceLevel InheritanceLevel { get; set; } = null!;
    public Year Year { get; set; } = null!;
    public BaseForCalculation BaseForCalculation { get; set; } = null!;
    public PayrollPeriod PayrollPeriod { get; set; } = null!;
}