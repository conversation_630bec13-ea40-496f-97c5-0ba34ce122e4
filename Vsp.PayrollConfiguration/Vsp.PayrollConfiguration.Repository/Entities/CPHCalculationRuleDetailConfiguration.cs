namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class CPHCalculationRuleDetailConfiguration : GeneratedIdEntityTypeConfigurationBase<CPHCalculationRuleDetail>
{
    public override void Configure(EntityTypeBuilder<CPHCalculationRuleDetail> builder)
    {
        base.Configure(builder);

        builder.ToTable("RekenRegelKpuDetail", "Ulsa");
        builder.Has<PERSON>ey(x => new { x.InheritanceLevelId, x.YearId, x.CPHCalculationRuleId, x.CPHCalculationRuleDetailId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.CPHCalculationRuleId).HasColumnName("RekenRegelKpuID");
        builder.Property(x => x.CPHCalculationRuleDetailId).HasColumnName("RekenRegelKpuDetailID");
        builder.Property(x => x.ComponentId).HasColumnName("ComponentID");

        builder.HasOne(x => x.ModelComponent).WithMany().IsRequired(false).HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.ComponentId });
    }
}