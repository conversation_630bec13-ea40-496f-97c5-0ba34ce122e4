namespace Vsp.PayrollConfiguration.Repository.Entities;

public class EmployeeProfile : GeneratedIdEntity, IMutableCodeTable
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int EmployeeProfileId { get; set; }

    public int EmployeeProfileIdDefinedAtLevel { get; set; }

    public string Omschrijving { get; set; } = null!;

    public int DescriptionDefinedAtLevel { get; set; }

    public Year Year { get; set; } = null!;
}
