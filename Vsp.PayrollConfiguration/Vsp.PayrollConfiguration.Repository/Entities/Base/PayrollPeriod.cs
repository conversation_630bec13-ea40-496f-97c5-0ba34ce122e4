namespace Vsp.PayrollConfiguration.Repository.Entities.Base;

public class PayrollPeriod : GeneratedIdEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int PayrollPeriodId { get; set; }

    public DateOnly StartDate { get; set; }
    public DateOnly EndDate { get; set; }
    public int? YearPeriod { get; set; }

    public Year Year { get; set; } = null!;
}