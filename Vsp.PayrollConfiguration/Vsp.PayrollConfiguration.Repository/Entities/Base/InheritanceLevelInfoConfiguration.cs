namespace Vsp.PayrollConfiguration.Repository.Entities.Base;

internal class InheritanceLevelInfoConfiguration : IEntityTypeConfiguration<InheritanceLevelInfo>
{
    public void Configure(EntityTypeBuilder<InheritanceLevelInfo> builder)
    {
        builder.ToTable("WerkgeverNiveau", "Ulsa");
        builder.<PERSON><PERSON><PERSON>(x => x.InheritanceLevelId);

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.ParentInheritanceLevelId).HasColumnName("LoonmodelID");
        builder.Property(x => x.GrandParentInheritanceLevelId).HasColumnName("CaoID");
    }
}
