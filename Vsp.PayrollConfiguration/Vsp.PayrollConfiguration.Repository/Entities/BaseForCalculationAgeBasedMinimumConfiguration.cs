namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class BaseForCalculationAgeBasedMinimumConfiguration : GeneratedIdEntityTypeConfigurationBase<BaseForCalculationAgeBasedMinimum>
{
    public override void Configure(EntityTypeBuilder<BaseForCalculationAgeBasedMinimum> builder)
    {
        base.Configure(builder);

        builder.ToTable("GrondslagMinimum", "Ulsa");
        builder.<PERSON><PERSON>ey(x => new { x.InheritanceLevelId, x.YearId, x.BaseForCalculationId, x.Age, x.PayrollPeriodId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.BaseForCalculationId).HasColumnName("GrondslagID");
        builder.Property(x => x.Age).HasColumnName("GrondslagMinimumID");
        builder.Property(x => x.PayrollPeriodId).HasColumnName("VerloningsPeriodeID");
        builder.Property(x => x.Minimum).HasColumnName("Bedrag");

        // DefinedAtLevel properties
        builder.Property(x => x.AgeDefinedAtLevel).HasColumnName("GrondslagMinimumIDAttribuut");
        builder.Property(x => x.PayrollPeriodDefinedAtLevel).HasColumnName("VerloningsPeriodeIDAttribuut");
        builder.Property(x => x.MinimumDefinedAtLevel).HasColumnName("BedragAttribuut");

        // Relationships
        builder.HasOne(x => x.InheritanceLevel).WithMany().HasForeignKey(x => x.InheritanceLevelId);
        builder.HasOne(x => x.Year).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId });
        builder.HasOne(x => x.BaseForCalculation).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.BaseForCalculationId, x.PayrollPeriodId });
        builder.HasOne(x => x.PayrollPeriod).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.PayrollPeriodId });
    }
}
