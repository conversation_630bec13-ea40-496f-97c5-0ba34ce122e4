namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class ModelBaseForCalculationBasePayrollComponentConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelBaseForCalculationBasePayrollComponent>
{
    public override void Configure(EntityTypeBuilder<ModelBaseForCalculationBasePayrollComponent> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelGrondslagBasis", "Ulsa");
        builder.HasKey(x => new { x.InheritanceLevelId, x.YearId, FoundationId = x.BaseForCalculationId, x.ComponentId, x.PayrollPeriodId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.BaseForCalculationId).HasColumnName("GrondslagID");
        builder.Property(x => x.ComponentId).HasColumnName("ComponentID");
        builder.Property(x => x.PayrollPeriodId).HasColumnName("VerloningsPeriodeID");
        builder.Property(x => x.Origin).HasColumnName("Herkomst");
    }
}