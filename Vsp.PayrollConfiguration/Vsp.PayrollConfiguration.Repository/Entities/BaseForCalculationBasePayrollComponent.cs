namespace Vsp.PayrollConfiguration.Repository.Entities;

public class BaseForCalculationBasePayrollComponent : GeneratedIdEntity, IInheritanceEntity, IPayrollPeriodEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int BaseForCalculationId { get; set; }
    [GeneratedIdKey<short>(3)]
    public int ComponentId { get; set; }
    [GeneratedIdKey<short>(4)]
    public int PayrollPeriodId { get; set; }

    public int BaseOrigin { get; set; }

    // DefinedAtLevel properties
    public int BaseForCalculationDefinedAtLevel { get; set; }
    public int ComponentDefinedAtLevel { get; set; }
    public int PayrollPeriodDefinedAtLevel { get; set; }
    public int BaseOriginDefinedAtLevel { get; set; }

    public InheritanceLevel InheritanceLevel { get; set; } = null!;
    public Year Year { get; set; } = null!;
    public BaseForCalculation BaseForCalculation { get; set; } = null!;
    public Component Component { get; set; } = null!;
    public ModelComponent? ModelComponent { get; set; }
    public PayrollPeriod PayrollPeriod { get; set; } = null!;

    public CtBaseOrigin CtBaseOrigin { get; set; } = null!;
}