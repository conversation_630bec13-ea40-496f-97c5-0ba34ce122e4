namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;

public static class EntityNames
{
    public const string CollectiveLaborAgreement = nameof(CollectiveLaborAgreement);
    public const string BaseForCalculation = nameof(BaseForCalculation);
    public const string BaseForCalculationBasePayrollComponent = nameof(BaseForCalculationBasePayrollComponent);
    public const string DifferentiatedReturnToWorkFund = nameof(DifferentiatedReturnToWorkFund);
    public const string PayrollAdministration = nameof(PayrollAdministration);
    public const string PayrollComponent = nameof(PayrollComponent);
    public const string PayrollComponentExtra = nameof(PayrollComponentExtra);
    public const string Shift = nameof(Shift);
    public const string UnitPercentage = nameof(UnitPercentage);
    public const string WageModel = nameof(WageModel);
    public const string Year = nameof(Year);
}

[CollectionDefinition(EntityNames.CollectiveLaborAgreement)]
public class CollectiveLaborAgreementTestsCollection : ICollectionFixture<WebApplicationFactoryFixture<Program, ILoketContext, LoketContext>>;

[CollectionDefinition(EntityNames.BaseForCalculation)]
public class BaseForCalculationTestsCollection : ICollectionFixture<WebApplicationFactoryFixture<Program, ILoketContext, LoketContext>>;

[CollectionDefinition(EntityNames.BaseForCalculationBasePayrollComponent)]
public class BaseForCalculationBasePayrollComponentTestsCollection : ICollectionFixture<WebApplicationFactoryFixture<Program, ILoketContext, LoketContext>>;

[CollectionDefinition(EntityNames.DifferentiatedReturnToWorkFund)]
public class DifferentiatedReturnToWorkFundTestsCollection : ICollectionFixture<WebApplicationFactoryFixture<Program, ILoketContext, LoketContext>>;

[CollectionDefinition(EntityNames.PayrollAdministration)]
public class PayrollAdministrationTestsCollection : ICollectionFixture<WebApplicationFactoryFixture<Program, ILoketContext, LoketContext>>;

[CollectionDefinition(EntityNames.PayrollComponent)]
public class PayrollComponentTestsCollection : ICollectionFixture<WebApplicationFactoryFixture<Program, ILoketContext, LoketContext>>;

[CollectionDefinition(EntityNames.PayrollComponentExtra)]
public class PayrollComponentExtraTestsCollection : ICollectionFixture<WebApplicationFactoryFixture<Program, ILoketContext, LoketContext>>;

[CollectionDefinition(EntityNames.Shift)]
public class ShiftTestsCollection : ICollectionFixture<WebApplicationFactoryFixture<Program, ILoketContext, LoketContext>>;

[CollectionDefinition(EntityNames.UnitPercentage)]
public class UnitPercentageTestsCollection : ICollectionFixture<WebApplicationFactoryFixture<Program, ILoketContext, LoketContext>>;

[CollectionDefinition(EntityNames.WageModel)]
public class WageModelTestsCollection : ICollectionFixture<WebApplicationFactoryFixture<Program, ILoketContext, LoketContext>>;

[CollectionDefinition(EntityNames.Year)]
public class YearTestsCollection : ICollectionFixture<WebApplicationFactoryFixture<Program, ILoketContext, LoketContext>>;
