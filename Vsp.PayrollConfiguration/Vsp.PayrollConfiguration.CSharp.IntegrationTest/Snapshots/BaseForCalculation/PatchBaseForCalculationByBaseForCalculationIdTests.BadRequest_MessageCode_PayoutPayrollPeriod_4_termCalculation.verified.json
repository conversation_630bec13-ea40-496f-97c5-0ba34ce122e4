{"content": null, "messages": [{"code": 0, "description": "Payout payroll period is only allowed with a cumulative base for calculation and with a term calculation via part-time.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_4", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}, {"code": 0, "description": "You have chosen a term calculation: not cumulative, not via part-time, and not automatic calculation. However, this type of calculation is no longer supported.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_BaseForCalculation_1", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}