{"content": {"administrationNumber": null, "clientNumber": "101611", "collectiveLaborAgreement": {"comment": "Empty CLA for unit and integration tests. DO NOT EDIT!", "description": "QA_PayrollConfiguration1_CLA_ForLevel_PA", "id": "dfdaa0a5-bce8-4ddd-8e1c-42554334c342"}, "employer": {"companyName": "QA_PayrollConfiguration1", "id": "6748429c-bcb6-4afa-9715-27d4bf00256d"}, "groupCode": 1337, "id": "bbe616a2-4e59-4d39-8165-ee37a9708be9", "name": "QA_PayrollConfiguration1_PA_ForLevel_PA", "wageModel": {"collectiveLaborAgreement": {"comment": "Empty CLA for unit and integration tests. DO NOT EDIT!", "description": "QA_PayrollConfiguration1_CLA_ForLevel_PA", "id": "dfdaa0a5-bce8-4ddd-8e1c-42554334c342"}, "comment": "Empty WM for unit and integration tests. DO NOT EDIT!", "description": "QA_PayrollConfiguration1_WM_ForLevel_PA", "id": "5e010bed-611d-469b-a220-5253e3896f47"}}, "messages": [], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}