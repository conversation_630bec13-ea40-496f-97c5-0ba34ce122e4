{"content": {"bonusPercentage": 14.5, "definedAtLevel": {"bonusPercentage": {"key": 2, "value": "WageModel"}, "fullTimeHoursPerWeek": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}}, "fullTimeHoursPerWeek": 1.2, "id": "Guid_1", "inheritanceLevel": {"id": "Guid_2", "type": {"key": 2, "value": "WageModel"}}, "shiftNumber": 2, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}, "messages": [], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}