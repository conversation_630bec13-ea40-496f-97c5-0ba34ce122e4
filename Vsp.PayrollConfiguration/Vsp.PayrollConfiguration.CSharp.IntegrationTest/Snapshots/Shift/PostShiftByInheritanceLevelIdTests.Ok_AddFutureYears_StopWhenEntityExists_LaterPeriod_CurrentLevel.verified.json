{"content": {"bonusPercentage": 10.5, "definedAtLevel": {"bonusPercentage": {"key": 3, "value": "PayrollAdministration"}, "fullTimeHoursPerWeek": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}}, "fullTimeHoursPerWeek": 1.23, "id": "Guid_1", "inheritanceLevel": {"id": "Guid_2", "type": {"key": 3, "value": "PayrollAdministration"}}, "shiftNumber": 4, "startPayrollPeriod": {"payrollPeriodId": 202101, "periodEndDate": "2021-01-31", "periodNumber": 1, "periodStartDate": "2021-01-01", "year": 2021}, "year": 2021}, "messages": [{"code": 0, "description": "Automatically added entity to future year(s) as well. See properties for details.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_Insert_Entity_AddedToFutureYear", "messageType": 6, "properties": "[2022]", "type": "Info"}], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}