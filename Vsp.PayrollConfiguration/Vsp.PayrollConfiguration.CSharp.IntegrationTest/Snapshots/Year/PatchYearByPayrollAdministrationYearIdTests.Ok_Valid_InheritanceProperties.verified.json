{"content": {"aof": null, "dateAvailableEss": "2025-04-30", "dateEssMail": null, "definedAtLevel": {"payrollPeriodType": {"key": 1, "value": "CollectiveLaborAgreement"}, "standardEmployeeProfile": {"key": 2, "value": "WageModel"}, "standardShift": {"key": 3, "value": "PayrollAdministration"}}, "id": "00000947-07e4-0000-0000-000000000000", "inheritanceLevel": {"id": "92a092b0-d6c9-46dc-8d88-ce006cfbffb4", "type": {"key": 3, "value": "PayrollAdministration"}}, "payrollPeriodType": {"key": 1, "value": "<PERSON><PERSON>"}, "sendEssMail": false, "standardEmployeeProfile": {"description": "WM employee profile 2020", "employeeProfileNumber": 1}, "standardShift": {"bonusPercentage": 5.02, "fullTimeHoursPerWeek": 38.2, "shiftNumber": 2}, "testYear": true, "year": 2020, "yearTransition": {"isPerformed": true, "isRequested": false, "performedDate": "2025-04-30"}, "zwSelfInsurerStartPayrollPeriod": null}, "messages": [], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}