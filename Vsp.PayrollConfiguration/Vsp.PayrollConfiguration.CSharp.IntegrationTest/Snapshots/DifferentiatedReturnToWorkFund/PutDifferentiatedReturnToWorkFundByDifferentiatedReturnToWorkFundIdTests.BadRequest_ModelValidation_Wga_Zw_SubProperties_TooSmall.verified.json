{"messages": [{"code": 0, "description": "", "exception": null, "id": null, "messageCode": "ModelStateValidationError", "messageType": 0, "properties": "{\"zw.TotalContribution\":\"TotalContribution must have a value between '-99,999' and '99,999'.\",\"wga.TotalContribution\":\"TotalContribution must have a value between '-99,999' and '99,999'.\",\"wga.EmploymentContribution\":\"EmploymentContribution must have a value between '0' and '99,999'.\"}", "type": "BrokenBusinessRule"}], "resultObject": null, "success": false}