{"content": {"balanceSheetSide": {"key": 2, "value": "Credit"}, "baseForCalculationBter": null, "category": {"key": 98, "value": "Intern"}, "column": {"key": 3, "value": "Aftrek alle heffingen"}, "costsEmployer": {"key": -1, "value": "-"}, "deductionOrPayment": {"key": 2, "value": "Inhouding"}, "definedAtLevel": {"balanceSheetSide": {"key": 3, "value": "PayrollAdministration"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 3, "value": "PayrollAdministration"}, "column": {"key": 3, "value": "PayrollAdministration"}, "costsEmployer": {"key": 3, "value": "PayrollAdministration"}, "deductionOrPayment": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "hoursIndication": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageZw": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationOvertime": {"key": 3, "value": "PayrollAdministration"}, "isFullTime": {"key": 3, "value": "PayrollAdministration"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 3, "value": "PayrollAdministration"}, "isPayment": {"key": 3, "value": "PayrollAdministration"}, "isTravelExpense": {"key": 3, "value": "PayrollAdministration"}, "paymentDescription": {"key": 3, "value": "PayrollAdministration"}, "paymentPeriod": {"key": 3, "value": "PayrollAdministration"}, "socialSecurityLiable": {"key": 3, "value": "PayrollAdministration"}, "suppressPrinting": {"key": 3, "value": "PayrollAdministration"}, "suppressPrintingAccumulations": {"key": 3, "value": "PayrollAdministration"}, "taxLiable": {"key": 3, "value": "PayrollAdministration"}}, "description": "PREMIE FDS26", "hoursIndication": null, "id": "00000990-07e9-07d1-0000-000000000000", "inheritanceLevel": {"id": "c6415e06-d1ed-4a75-86ba-5415eea6c9fa", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 2001, "paymentDescription": null, "paymentPeriod": {"key": 7, "value": "Herberekenen bij gebroken periode"}, "socialSecurityLiable": {"key": 2, "value": "-"}, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": {"key": 2, "value": "Tabelloon -"}, "year": 2025}, "messages": [], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}