{"_embedded": [{"balanceSheetSide": {"key": 1, "value": "Debet"}, "baseForCalculationBter": null, "category": {"key": 26, "value": "Output"}, "column": {"key": 1, "value": "Loon in geld"}, "costsEmployer": {"key": 1, "value": "+"}, "deductionOrPayment": {"key": 1, "value": "<PERSON><PERSON>"}, "definedAtLevel": {"balanceSheetSide": {"key": 1, "value": "CollectiveLaborAgreement"}, "baseForCalculationBter": {"key": 1, "value": "CollectiveLaborAgreement"}, "category": {"key": 1, "value": "CollectiveLaborAgreement"}, "column": {"key": 1, "value": "CollectiveLaborAgreement"}, "costsEmployer": {"key": 1, "value": "CollectiveLaborAgreement"}, "deductionOrPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "description": {"key": 1, "value": "CollectiveLaborAgreement"}, "hoursIndication": {"key": 1, "value": "CollectiveLaborAgreement"}, "id": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationDailyWageSupplement": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationDailyWageZw": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isFullTime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isNetToGross": {"key": 1, "value": "CollectiveLaborAgreement"}, "isOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isTravelExpense": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentDescription": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "socialSecurityLiable": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrinting": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrintingAccumulations": {"key": 1, "value": "CollectiveLaborAgreement"}, "taxLiable": {"key": 1, "value": "CollectiveLaborAgreement"}}, "description": "SUPPLETIE_CLA", "hoursIndication": null, "id": "00000961-07e9-0064-0000-000000000000", "inheritanceLevel": {"id": "82ac85be-082b-4b72-b719-5b47098a122b", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 100, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": {"key": 1, "value": "+"}, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": {"key": 1, "value": "Tabelloon +"}, "year": 2025}, {"balanceSheetSide": {"key": 1, "value": "Debet"}, "baseForCalculationBter": null, "category": {"key": 30, "value": "Bedrag per eenheid"}, "column": {"key": 13, "value": "<PERSON><PERSON> beta<PERSON>"}, "costsEmployer": {"key": 1, "value": "+"}, "deductionOrPayment": {"key": 1, "value": "<PERSON><PERSON>"}, "definedAtLevel": {"balanceSheetSide": {"key": 1, "value": "CollectiveLaborAgreement"}, "baseForCalculationBter": {"key": 1, "value": "CollectiveLaborAgreement"}, "category": {"key": 1, "value": "CollectiveLaborAgreement"}, "column": {"key": 1, "value": "CollectiveLaborAgreement"}, "costsEmployer": {"key": 1, "value": "CollectiveLaborAgreement"}, "deductionOrPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "description": {"key": 1, "value": "CollectiveLaborAgreement"}, "hoursIndication": {"key": 1, "value": "CollectiveLaborAgreement"}, "id": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationDailyWageSupplement": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationDailyWageZw": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isFullTime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isNetToGross": {"key": 1, "value": "CollectiveLaborAgreement"}, "isOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isTravelExpense": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentDescription": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "socialSecurityLiable": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrinting": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrintingAccumulations": {"key": 1, "value": "CollectiveLaborAgreement"}, "taxLiable": {"key": 1, "value": "CollectiveLaborAgreement"}}, "description": "KM.VERGOED_CLA", "hoursIndication": null, "id": "00000961-07e9-0065-0000-000000000000", "inheritanceLevel": {"id": "82ac85be-082b-4b72-b719-5b47098a122b", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": true, "key": 101, "paymentDescription": null, "paymentPeriod": {"key": 5, "value": "<PERSON><PERSON><PERSON><PERSON>"}, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": {"key": 1, "value": "Debet"}, "baseForCalculationBter": null, "category": {"key": 30, "value": "Bedrag per eenheid"}, "column": {"key": 1, "value": "Loon in geld"}, "costsEmployer": {"key": 1, "value": "+"}, "deductionOrPayment": {"key": 1, "value": "<PERSON><PERSON>"}, "definedAtLevel": {"balanceSheetSide": {"key": 1, "value": "CollectiveLaborAgreement"}, "baseForCalculationBter": {"key": 1, "value": "CollectiveLaborAgreement"}, "category": {"key": 1, "value": "CollectiveLaborAgreement"}, "column": {"key": 1, "value": "CollectiveLaborAgreement"}, "costsEmployer": {"key": 1, "value": "CollectiveLaborAgreement"}, "deductionOrPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "description": {"key": 2, "value": "WageModel"}, "hoursIndication": {"key": 1, "value": "CollectiveLaborAgreement"}, "id": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageSupplement": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationDailyWageZw": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isFullTime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isNetToGross": {"key": 1, "value": "CollectiveLaborAgreement"}, "isOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isTravelExpense": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentDescription": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "socialSecurityLiable": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrinting": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrintingAccumulations": {"key": 1, "value": "CollectiveLaborAgreement"}, "taxLiable": {"key": 1, "value": "CollectiveLaborAgreement"}}, "description": "KM.VERGOED_WM", "hoursIndication": null, "id": "00000961-07e9-0066-0000-000000000000", "inheritanceLevel": {"id": "82ac85be-082b-4b72-b719-5b47098a122b", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 102, "paymentDescription": null, "paymentPeriod": {"key": 5, "value": "<PERSON><PERSON><PERSON><PERSON>"}, "socialSecurityLiable": {"key": 1, "value": "+"}, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": {"key": 3, "value": "Tariefloon +"}, "year": 2025}, {"balanceSheetSide": {"key": 1, "value": "Debet"}, "baseForCalculationBter": null, "category": {"key": 9, "value": "Netto betaling"}, "column": {"key": 13, "value": "<PERSON><PERSON> beta<PERSON>"}, "costsEmployer": {"key": 1, "value": "+"}, "deductionOrPayment": {"key": 1, "value": "<PERSON><PERSON>"}, "definedAtLevel": {"balanceSheetSide": {"key": 3, "value": "PayrollAdministration"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 3, "value": "PayrollAdministration"}, "column": {"key": 3, "value": "PayrollAdministration"}, "costsEmployer": {"key": 3, "value": "PayrollAdministration"}, "deductionOrPayment": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "hoursIndication": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageZw": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationOvertime": {"key": 3, "value": "PayrollAdministration"}, "isFullTime": {"key": 3, "value": "PayrollAdministration"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 3, "value": "PayrollAdministration"}, "isPayment": {"key": 3, "value": "PayrollAdministration"}, "isTravelExpense": {"key": 3, "value": "PayrollAdministration"}, "paymentDescription": {"key": 3, "value": "PayrollAdministration"}, "paymentPeriod": {"key": 3, "value": "PayrollAdministration"}, "socialSecurityLiable": {"key": 3, "value": "PayrollAdministration"}, "suppressPrinting": {"key": 3, "value": "PayrollAdministration"}, "suppressPrintingAccumulations": {"key": 3, "value": "PayrollAdministration"}, "taxLiable": {"key": 3, "value": "PayrollAdministration"}}, "description": "VRY NETTO B_PA", "hoursIndication": null, "id": "00000961-07e9-012c-0000-000000000000", "inheritanceLevel": {"id": "82ac85be-082b-4b72-b719-5b47098a122b", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 300, "paymentDescription": null, "paymentPeriod": {"key": 7, "value": "Herberekenen bij gebroken periode"}, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 4, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}