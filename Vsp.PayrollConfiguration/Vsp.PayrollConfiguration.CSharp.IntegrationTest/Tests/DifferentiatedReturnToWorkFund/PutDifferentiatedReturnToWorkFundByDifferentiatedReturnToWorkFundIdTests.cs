using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.DifferentiatedReturnToWorkFund.Constants;
using Vsp.PayrollConfiguration.Domain.DifferentiatedReturnToWorkFund.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.DifferentiatedReturnToWorkFund;

[Collection(EntityNames.DifferentiatedReturnToWorkFund)]
public class PutDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "DifferentiatedReturnToWorkFund";
    protected override bool UseTransaction => true;

    private static readonly Guid QA_DifferentiatedReturnToWorkFund_PUT_PA = Guid.Parse("e9a02fbd-be45-4c5e-92c8-d5de242a2761");

    private static readonly Guid DifferentiatedReturnToWorkFund = Guid.Parse("f73f02f4-5cf4-41c5-810f-d6d5923957b5"); // In QA_DifferentiatedReturnToWorkFund_PUT_PA

    [Fact]
    public async Task Ok_QA_DifferentiatedReturnToWorkFund_PUT_PA()
    {
        // Arrange
        var postUri =
            $"{DifferentiatedReturnToWorkFundRoutes.PostDifferentiatedReturnToWorkFundsByPayrollAdministrationIdAsync}"
                .Replace("{payrollAdministrationId:guid}", QA_DifferentiatedReturnToWorkFund_PUT_PA.ToString());
        var postModel = new DifferentiatedReturnToWorkFundPostModel
        {
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 2 },
            Wga = new() { TotalContribution = 2.123m, EmploymentContribution = 1.001m },
            Zw = new() { TotalContribution = 3.123m },
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);
        var postObject = JsonConvert.DeserializeObject<DetailResult<DifferentiatedReturnToWorkFundModel>>(postResult!)!;
        var differentiatedReturnToWorkFundId = postObject.Content!.Id;

        // Act
        var putUri =
            $"{DifferentiatedReturnToWorkFundRoutes.PutDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdAsync}"
                .Replace("{differentiatedReturnToWorkFundId:guid}", differentiatedReturnToWorkFundId.ToString());
        var putModel = new DifferentiatedReturnToWorkFundPutModel
        {
            Wga = new() { TotalContribution = 2.321m, EmploymentContribution = 1.032m },
            Zw = new() { TotalContribution = 3.321m },
        };
        var putResult = await CallApiAsync(HttpMethod.Put, putUri, putModel, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(putResult, scrubGuids: true);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Null()
    {
        // Act
        var putUri =
            $"{DifferentiatedReturnToWorkFundRoutes.PutDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdAsync}"
                .Replace("{differentiatedReturnToWorkFundId:guid}", DifferentiatedReturnToWorkFund.ToString());
        var putResult = await CallApiAsync(HttpMethod.Put, putUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(putResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Wga_Zw_Null()
    {
        // Act
        var putUri =
            $"{DifferentiatedReturnToWorkFundRoutes.PutDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdAsync}"
                .Replace("{differentiatedReturnToWorkFundId:guid}", DifferentiatedReturnToWorkFund.ToString());
        var putModel = new DifferentiatedReturnToWorkFundPutModel();
        var putResult = await CallApiAsync(HttpMethod.Put, putUri, putModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(putResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Wga_Zw_SubProperties_Null()
    {
        // Act
        var putUri =
            $"{DifferentiatedReturnToWorkFundRoutes.PutDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdAsync}"
                .Replace("{differentiatedReturnToWorkFundId:guid}", DifferentiatedReturnToWorkFund.ToString());
        var putModel = new DifferentiatedReturnToWorkFundPutModel { Wga = new(), Zw = new() };
        var putResult = await CallApiAsync(HttpMethod.Put, putUri, putModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(putResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Wga_Zw_SubProperties_TooSmall()
    {
        // Act
        var putUri =
            $"{DifferentiatedReturnToWorkFundRoutes.PutDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdAsync}"
                .Replace("{differentiatedReturnToWorkFundId:guid}", DifferentiatedReturnToWorkFund.ToString());
        var putModel = new DifferentiatedReturnToWorkFundPutModel
        {
            Wga = new() { TotalContribution = -100, EmploymentContribution = -0.001M },
            Zw = new() { TotalContribution = -100 },
        };
        var putResult = await CallApiAsync(HttpMethod.Put, putUri, putModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(putResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Wga_Zw_SubProperties_TooBig()
    {
        // Act
        var putUri =
            $"{DifferentiatedReturnToWorkFundRoutes.PutDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdAsync}"
                .Replace("{differentiatedReturnToWorkFundId:guid}", DifferentiatedReturnToWorkFund.ToString());
        var putModel = new DifferentiatedReturnToWorkFundPutModel
        {
            Wga = new() { TotalContribution = 100, EmploymentContribution = 100 },
            Zw = new() { TotalContribution = 100 },
        };
        var putResult = await CallApiAsync(HttpMethod.Put, putUri, putModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(putResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Wga_Zw_SubProperties_TooManyDecimals()
    {
        // Act
        var putUri =
            $"{DifferentiatedReturnToWorkFundRoutes.PutDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdAsync}"
                .Replace("{differentiatedReturnToWorkFundId:guid}", DifferentiatedReturnToWorkFund.ToString());
        var putJson =
            """
            {
                "Wga": { "TotalContribution": 0.1234, "EmploymentContribution": 0.1234 },
                "Zw": { "TotalContribution": 0.1234 }
            }
            """;
        var putResult = await CallApiAsync(HttpMethod.Put, putUri, putJson, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(putResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_DifferentiatedReturnToWorkFund_WgaZwTotalContributionOutOfRange"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_WgaZwTotalContributionOutOfRange_TooSmall()
    {
        // Act
        var putUri =
            $"{DifferentiatedReturnToWorkFundRoutes.PutDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdAsync}"
                .Replace("{differentiatedReturnToWorkFundId:guid}", DifferentiatedReturnToWorkFund.ToString());
        var putModel = new DifferentiatedReturnToWorkFundPutModel
        {
            Wga = new() { TotalContribution = -50, EmploymentContribution = 0 },
            Zw = new() { TotalContribution = -50 },
        };
        var putResult = await CallApiAsync(HttpMethod.Put, putUri, putModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(putResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_DifferentiatedReturnToWorkFund_WgaZwTotalContributionOutOfRange"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_WgaZwTotalContributionOutOfRange_TooBig()
    {
        // Act
        var putUri =
            $"{DifferentiatedReturnToWorkFundRoutes.PutDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdAsync}"
                .Replace("{differentiatedReturnToWorkFundId:guid}", DifferentiatedReturnToWorkFund.ToString());
        var putModel = new DifferentiatedReturnToWorkFundPutModel
        {
            Wga = new() { TotalContribution = 50, EmploymentContribution = 1 },
            Zw = new() { TotalContribution = 50 },
        };
        var putResult = await CallApiAsync(HttpMethod.Put, putUri, putModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(putResult);
    }
    
    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_DifferentiatedReturnToWorkFund_WgaTotalContributionZeroOrNegative_WgaEmploymentContributionNonZero"/>
    /// </summary>
    [Theory]
    [InlineData(0, 0.001)]
    [InlineData(-3, 0.001)]
    public async Task BadRequest_MessageCode_WgaTotalContributionZeroOrNegative_WgaEmploymentContributionNonZero(decimal wgaTotalContribution, decimal wgaEmploymentContribution)
    {
        var putUri =
            $"{DifferentiatedReturnToWorkFundRoutes.PutDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdAsync}"
                .Replace("{differentiatedReturnToWorkFundId:guid}", DifferentiatedReturnToWorkFund.ToString());
        var putModel = new DifferentiatedReturnToWorkFundPutModel
        {
            Wga = new() { TotalContribution = wgaTotalContribution, EmploymentContribution = wgaEmploymentContribution },
            Zw = new() { TotalContribution = 1 },
        };
        var putResult = await CallApiAsync(HttpMethod.Put, putUri, putModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(putResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_DifferentiatedReturnToWorkFund_WgaTotalContributionPositive_WgaEmploymentContributionGreaterThanHalf"/>
    /// </summary>
    [Theory]
    [InlineData(50, 25.001)]
    [InlineData(10.691, 5.346)]
    public async Task BadRequest_MessageCode_WgaTotalContributionPositive_WgaEmploymentContributionGreaterThanHalf(decimal wgaTotalContribution, decimal wgaEmploymentContribution)
    {
        var putUri =
            $"{DifferentiatedReturnToWorkFundRoutes.PutDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdAsync}"
                .Replace("{differentiatedReturnToWorkFundId:guid}", DifferentiatedReturnToWorkFund.ToString());
        var putModel = new DifferentiatedReturnToWorkFundPutModel
        {
            Wga = new() { TotalContribution = wgaTotalContribution, EmploymentContribution = wgaEmploymentContribution },
            Zw = new() { TotalContribution = 1 },
        };
        var putResult = await CallApiAsync(HttpMethod.Put, putUri, putModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(putResult);
    }
}