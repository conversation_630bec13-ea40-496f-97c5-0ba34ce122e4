using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.PayrollComponent.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.PayrollComponent;

[Collection(EntityNames.PayrollComponent)]
public class GetPayrollComponentMetadataByProviderIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture) : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "PayrollComponent";
    protected override bool UseTransaction => false;
    
    private static readonly string QA_PayrollConfiguration1 = "db6ac336-bcc3-42b7-b40a-b0d25f66c83f";

    [Fact]
    public async Task Ok()
    {
        // Act
        var getUri = PayrollComponentRoutes.GetPayrollComponentMetadataByProviderIdAsync.Replace("{providerId:guid}", QA_PayrollConfiguration1);
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
}