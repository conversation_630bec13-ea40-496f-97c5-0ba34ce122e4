using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.PayrollComponent.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.PayrollComponent;

[Collection(EntityNames.PayrollComponent)]
public class GetAvailablePayrollComponentsByYearIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "PayrollComponent";
    protected override bool UseTransaction => false;

    private static readonly Guid QA_PayrollComponent_GET_CLA_2025 = Guid.Parse("0000095e-07e9-0000-0000-000000000000");
    private static readonly Guid QA_PayrollComponent_GET_WM_2025 = Guid.Parse("00000960-07e9-0000-0000-000000000000");
    private static readonly Guid QA_PayrollComponent_GET_PA_2025 = Guid.Parse("00000961-07e9-0000-0000-000000000000");

    private const string OrderBy = "orderBy=category.value,description";
    private const string OrderByDesc = "orderBy=-category.value,-description";
    private const string FilterBy = "filter=category.value lk 'Bruto vergoeding tabel' and description lk 'VRY' and key lk '1'";
    
    private const string ExclusionCategory99FilterBy = "filter=category.key eq 99";
    private const string Exclusion498FilterBy = "filter=key eq 498";
    private const string Exclusion_ORT_OVW_FilterBy = "filter=key eq 489 or key eq 490 or key eq 491 or key eq 492 or key eq 4015 or key eq 4016 or key eq 4017 or key eq 4018 or key eq 4019";

    [Fact]
    public async Task Ok_QA_PayrollComponent_GET_CLA_2025()
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetAvailablePayrollComponentsByYearIdAsync}"
                .Replace("{yearId:guid}", QA_PayrollComponent_GET_CLA_2025.ToString()) +
            $"?pageSize=9999&{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_QA_PayrollComponent_GET_WM_2025()
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetAvailablePayrollComponentsByYearIdAsync}"
                .Replace("{yearId:guid}", QA_PayrollComponent_GET_WM_2025.ToString()) +
            $"?pageSize=9999&{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_QA_PayrollComponent_GET_PA_2025()
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetAvailablePayrollComponentsByYearIdAsync}"
                .Replace("{yearId:guid}", QA_PayrollComponent_GET_PA_2025.ToString()) +
            $"?pageSize=9999&{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_OrderBy_Key_Desc_QA_PayrollComponent_GET_PA_2025()
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetAvailablePayrollComponentsByYearIdAsync}"
                .Replace("{yearId:guid}", QA_PayrollComponent_GET_PA_2025.ToString()) +
            $"?{OrderByDesc}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_Filter_QA_PayrollComponent_GET_PA_2025()
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetAvailablePayrollComponentsByYearIdAsync}"
                .Replace("{yearId:guid}", QA_PayrollComponent_GET_PA_2025.ToString()) +
            $"?{OrderBy}&{FilterBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
    
    [Theory]
    [InlineData("00000972-07e0-0000-0000-000000000000")]// QA_PayrollComponent_GET_CLA_2016 year 2016
    [InlineData("00000973-07e0-0000-0000-000000000000")]// QA_PayrollComponent_GET_WM_2016 year 2016
    [InlineData("00000974-07e0-0000-0000-000000000000")]// QA_PayrollComponent_GET_PA_2016 year 2016
    public async Task Ok_QA_PayrollComponent_Exclude_498_Before_2017(string yearId)
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetAvailablePayrollComponentsByYearIdAsync}"
                .Replace("{yearId:guid}", yearId) +
            $"?{Exclusion498FilterBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
    
    [Theory]
    [InlineData("00000972-07e2-0000-0000-000000000000")]// QA_PayrollComponent_GET_CLA_2016 year 2018
    [InlineData("00000973-07e2-0000-0000-000000000000")]// QA_PayrollComponent_GET_WM_2016 year 2018
    [InlineData("00000974-07e2-0000-0000-000000000000")]// QA_PayrollComponent_GET_PA_2016 year 2018
    [InlineData("00000972-07e3-0000-0000-000000000000")]// QA_PayrollComponent_GET_CLA_2016 year 2019
    [InlineData("00000973-07e3-0000-0000-000000000000")]// QA_PayrollComponent_GET_WM_2016 year 2019
    [InlineData("00000974-07e3-0000-0000-000000000000")]// QA_PayrollComponent_GET_PA_2016 year 2019
    public async Task Ok_QA_PayrollComponent_Not_Exclude_498_After_2017(string yearId)
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetAvailablePayrollComponentsByYearIdAsync}"
                .Replace("{yearId:guid}", yearId) +
            $"?{Exclusion498FilterBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
    
    [Theory]
    [InlineData("00000972-07e1-0000-0000-000000000000")]// QA_PayrollComponent_GET_CLA_2016 year 2017
    [InlineData("00000973-07e1-0000-0000-000000000000")]// QA_PayrollComponent_GET_WM_2016 year 2017
    [InlineData("00000974-07e1-0000-0000-000000000000")]// QA_PayrollComponent_GET_PA_2016 year 2017
    public async Task Ok_QA_PayrollComponent_Exclude_498_In_2017_ExceptForPA(string yearId)
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetAvailablePayrollComponentsByYearIdAsync}"
                .Replace("{yearId:guid}", yearId) +
            $"?{Exclusion498FilterBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
    
    [Theory]
    [InlineData("00000972-07e1-0000-0000-000000000000")]// QA_PayrollComponent_GET_CLA_2016 year 2017
    [InlineData("00000972-07e2-0000-0000-000000000000")]// QA_PayrollComponent_GET_CLA_2016 year 2018
    [InlineData("00000973-07e1-0000-0000-000000000000")]// QA_PayrollComponent_GET_WM_2016 year 2017
    [InlineData("00000973-07e2-0000-0000-000000000000")]// QA_PayrollComponent_GET_WM_2016 year 2018
    [InlineData("00000974-07e1-0000-0000-000000000000")]// QA_PayrollComponent_GET_PA_2016 year 2017
    [InlineData("00000974-07e2-0000-0000-000000000000")]// QA_PayrollComponent_GET_PA_2016 year 2018
    public async Task Ok_QA_PayrollComponent_Exclude_ORT_OVW_Before_2019(string yearId)
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetAvailablePayrollComponentsByYearIdAsync}"
                .Replace("{yearId:guid}", yearId) +
            $"?{Exclusion_ORT_OVW_FilterBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
    
    [Theory]
    [InlineData("00000972-07e3-0000-0000-000000000000")]// QA_PayrollComponent_GET_CLA_2016 year 2019
    [InlineData("00000973-07e3-0000-0000-000000000000")]// QA_PayrollComponent_GET_WM_2016 year 2019
    [InlineData("00000974-07e3-0000-0000-000000000000")]// QA_PayrollComponent_GET_PA_2016 year 2019
    [InlineData("00000972-07e4-0000-0000-000000000000")]// QA_PayrollComponent_GET_CLA_2016 year 2020
    [InlineData("00000973-07e4-0000-0000-000000000000")]// QA_PayrollComponent_GET_WM_2016 year 2020
    [InlineData("00000974-07e4-0000-0000-000000000000")]// QA_PayrollComponent_GET_PA_2016 year 2020
    public async Task Ok_QA_PayrollComponent_Not_Exclude_ORT_OVW_From_2019_On(string yearId)
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetAvailablePayrollComponentsByYearIdAsync}"
                .Replace("{yearId:guid}", yearId) +
            $"?{Exclusion_ORT_OVW_FilterBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
    
    [Theory]
    [InlineData("00000972-07e0-0000-0000-000000000000")]// QA_PayrollComponent_GET_CLA_2016 year 2016
    [InlineData("00000973-07e0-0000-0000-000000000000")]// QA_PayrollComponent_GET_WM_2016 year 2016
    [InlineData("00000974-07e0-0000-0000-000000000000")]// QA_PayrollComponent_GET_PA_2016 year 2016
    [InlineData("00000972-07e1-0000-0000-000000000000")]// QA_PayrollComponent_GET_CLA_2016 year 2017
    [InlineData("00000973-07e1-0000-0000-000000000000")]// QA_PayrollComponent_GET_WM_2016 year 2017
    [InlineData("00000974-07e1-0000-0000-000000000000")]// QA_PayrollComponent_GET_PA_2016 year 2017
    [InlineData("00000972-07e3-0000-0000-000000000000")]// QA_PayrollComponent_GET_CLA_2016 year 2019
    [InlineData("00000973-07e3-0000-0000-000000000000")]// QA_PayrollComponent_GET_WM_2016 year 2019
    [InlineData("00000974-07e3-0000-0000-000000000000")]// QA_PayrollComponent_GET_PA_2016 year 2019
    public async Task Ok_QA_PayrollComponent_Exclude_Category_99(string yearId)
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetAvailablePayrollComponentsByYearIdAsync}"
                .Replace("{yearId:guid}", yearId) +
            $"?{ExclusionCategory99FilterBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
}