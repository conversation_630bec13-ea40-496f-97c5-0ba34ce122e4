using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Year.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.Year;

[Collection(EntityNames.Year)]
public class GetYearsMinimizedByInheritanceLevelIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "Year";
    protected override bool UseTransaction => false;
    
    private const string OrderBy = "orderBy=year";

    private const string OrderByDesc = "orderBy=-year";

    private const string FilterYearId = "0000093b-07e9-0000-0000-000000000000"; // 2025 at CLA level
    private const string FilterUnexistentYearId = "0000093b-07e3-0000-0000-000000000000"; // 2019 at CLA level
    
    private static readonly Guid QA_Year_GET_CLA = Guid.Parse("6a1d3e98-8b9f-493f-b9b6-2c52655f49d1");
    private static readonly Guid QA_Year_GET_WM = Guid.Parse("458d0541-8bdb-4d35-8c6e-606297d15db8");
    private static readonly Guid QA_Year_GET_PA = Guid.Parse("d8706816-6153-455a-934d-4e939a2af6c3");

    [Fact]
    public async Task Ok_OrderBy_Year_For_CLA()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsMinimizedByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_Year_GET_CLA}&{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_OrderBy_Year_For_WM()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsMinimizedByInheritanceLevelIdAsync}?wageModelId={QA_Year_GET_WM}&{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);
    
        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }
    
    [Fact]
    public async Task Ok_OrderBy_Year_For_PA()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsMinimizedByInheritanceLevelIdAsync}?payrollAdministrationId={QA_Year_GET_PA}&{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);
    
        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_OrderBy_Year_Desc_For_CLA()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsMinimizedByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_Year_GET_CLA}&{OrderByDesc}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_FilterBy_Id_For_CLA()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsMinimizedByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_Year_GET_CLA}&{OrderBy}&filter=id eq '{FilterYearId}'";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_Empty_For_CLA()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsMinimizedByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_Year_GET_CLA}&{OrderBy}&filter=id eq '{FilterUnexistentYearId}'";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }
}