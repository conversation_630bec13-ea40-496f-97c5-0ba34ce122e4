<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <AnalysisLevel>latest</AnalysisLevel>
    <IsPackable>false</IsPackable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageReference Include="MSTest.TestAdapter" Version="3.9.3" />
    <PackageReference Include="MSTest.TestFramework" Version="3.9.3" />
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Vsp.UnitTesting" Version="9.0.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Vsp.PayrollConfiguration.Repository\Vsp.PayrollConfiguration.Repository.csproj" />
  </ItemGroup>
</Project>
