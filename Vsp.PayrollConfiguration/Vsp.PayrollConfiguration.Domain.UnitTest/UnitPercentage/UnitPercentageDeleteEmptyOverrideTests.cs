using System.Reflection;
using System.Text;
using Vsp.PayrollConfiguration.Domain.UnitTest.Shared;

namespace Vsp.PayrollConfiguration.Domain.UnitTest.UnitPercentage;

[TestClass]
public class UnitPercentageDeleteEmptyOverrideTests
{
    private static IConfiguration configuration;
    private LoketContext loketContext;

    private InheritanceLevel collectiveLaborAgreement;
    private InheritanceLevel wageModel;
    private InheritanceLevel payrollAdministration;

    [ClassInitialize]
    public static void ClassInitialize(TestContext _) => configuration = new ConfigurationBuilder().AddJsonFile("appsettings.Test.json").Build();

    [TestInitialize]
    public void TestInitialize()
    {
        this.loketContext = MockContextCreator.Create<LoketContext>(sqlConnectionString: configuration.GetConnectionString("VspQA"), null);

        this.payrollAdministration = this.loketContext.InheritanceLevels
            .Include(x => x.ParentInheritanceLevel).ThenInclude(x => x.ParentInheritanceLevel)
            .Where(x => x.Id == SharedConstants.PayrollAdministrations.QA_PayrollConfiguration1_PA_ForLevel_PA)
            .Single();

        this.wageModel = this.payrollAdministration.ParentInheritanceLevel;
        this.collectiveLaborAgreement = this.wageModel.ParentInheritanceLevel;
    }

    [TestCleanup]
    public void TestCleanup() => this.loketContext.Dispose();

    [TestMethod]
    [DynamicData(nameof(Permutations), DynamicDataDisplayName = nameof(GetDynamicDataDisplayName))]
    public async Task UnitPercentageDeleteEmptyOverrideTest(UnitPercentageDeleteEmptyOverrideTestCase testCase)
    {
        // Arrange
        var yearId = 2025;
        var componentId = 1;
        var payrollPeriodId = 1;

        if (testCase.CLA != null)
        {
            var unitpercentage = new ModelUnitPercentage
            {
                InheritanceLevelId = this.collectiveLaborAgreement.InheritanceLevelId,
                YearId = yearId,
                ComponentId = componentId,
                PayrollPeriodId = payrollPeriodId,

                Percentage = testCase.CLA.Percentage,
                CalculateOver = testCase.CLA.CalculateOver,
            };
            this.loketContext.Add(unitpercentage);
        }
        if (testCase.WM != null)
        {
            var unitpercentage = new ModelUnitPercentage
            {
                InheritanceLevelId = this.wageModel.InheritanceLevelId,
                YearId = yearId,
                ComponentId = componentId,
                PayrollPeriodId = payrollPeriodId,

                Percentage = testCase.WM.Percentage,
                CalculateOver = testCase.WM.CalculateOver,
            };
            this.loketContext.Add(unitpercentage);
        }
        if (testCase.PA != null)
        {
            var unitpercentage = new ModelUnitPercentage
            {
                InheritanceLevelId = this.payrollAdministration.InheritanceLevelId,
                YearId = yearId,
                ComponentId = componentId,
                PayrollPeriodId = payrollPeriodId,

                Percentage = testCase.PA.Percentage,
                CalculateOver = testCase.PA.CalculateOver,
            };
            this.loketContext.Add(unitpercentage);
        }
        await this.loketContext.SaveChangesAsync();

        var preWageModel = await this.loketContext.Set<Repository.Entities.UnitPercentage>().AsNoTracking()
            .Where(x => x.InheritanceLevelId == this.wageModel.InheritanceLevelId && x.YearId == yearId && x.ComponentId == componentId && x.PayrollPeriodId == payrollPeriodId)
            .SingleAsync();

        var prePayrollAdministration = await this.loketContext.Set<Repository.Entities.UnitPercentage>().AsNoTracking()
            .Where(x => x.InheritanceLevelId == this.payrollAdministration.InheritanceLevelId && x.YearId == yearId && x.ComponentId == componentId && x.PayrollPeriodId == payrollPeriodId)
            .SingleAsync();

        // Act
        switch (testCase.Delete)
        {
            case Delete.WM:
                await this.loketContext.Set<ModelUnitPercentage>()
                    .Where(x => x.InheritanceLevelId == this.wageModel.InheritanceLevelId && x.YearId == yearId && x.ComponentId == componentId && x.PayrollPeriodId == payrollPeriodId)
                    .ExecuteDeleteAsync();
                break;

            case Delete.PA:
                await this.loketContext.Set<ModelUnitPercentage>()
                    .Where(x => x.InheritanceLevelId == this.payrollAdministration.InheritanceLevelId && x.YearId == yearId && x.ComponentId == componentId && x.PayrollPeriodId == payrollPeriodId)
                    .ExecuteDeleteAsync();
                break;
        }

        // Assert
        var postWageModel = await this.loketContext.Set<Repository.Entities.UnitPercentage>().AsNoTracking()
            .Where(x => x.InheritanceLevelId == this.wageModel.InheritanceLevelId && x.YearId == yearId && x.ComponentId == componentId && x.PayrollPeriodId == payrollPeriodId)
            .SingleAsync();

        var postPayrollAdministration = await this.loketContext.Set<Repository.Entities.UnitPercentage>().AsNoTracking()
            .Where(x => x.InheritanceLevelId == this.payrollAdministration.InheritanceLevelId && x.YearId == yearId && x.ComponentId == componentId && x.PayrollPeriodId == payrollPeriodId)
            .SingleAsync();

        postWageModel.Should().BeEquivalentTo(preWageModel, opt => opt.Excluding(x => x.ComponentIdDefinedAtLevel));
        postPayrollAdministration.Should().BeEquivalentTo(prePayrollAdministration, opt => opt.Excluding(x => x.ComponentIdDefinedAtLevel));

        switch (testCase.Delete)
        {
            case Delete.WM:
                postWageModel.ComponentIdDefinedAtLevel.Should().Be(1);
                postPayrollAdministration.ComponentIdDefinedAtLevel.Should().Be(testCase.PA != null ? 3 : 1);
                break;

            case Delete.PA:
                postWageModel.ComponentIdDefinedAtLevel.Should().Be(testCase.WM != null ? 2 : 1);
                postPayrollAdministration.ComponentIdDefinedAtLevel.Should().Be(testCase.WM != null ? 2 : 1);
                break;
        }
    }

    public static string GetDynamicDataDisplayName(MethodInfo _, object[] data) => data[0].ToString();

    public static IEnumerable<object[]> Permutations
    {
        get
        {
            var result = new List<object[]>();

            var deletes = Enum.GetValues<Delete>();

            var clas = new UnitPercentageValues[]
            {
                null,
                new(11, 0),
            };

            var wms = new UnitPercentageValues[]
            {
                null,
                UnitPercentageValues.Empty,
                new(null, 1),
                new(21, null),
                new(21, 1),
            };

            var pas = new UnitPercentageValues[]
            {
                null,
                UnitPercentageValues.Empty,
                new(null, 2),
                new(31, null),
                new(31, 2),
            };

            for (var d = 0; d < deletes.Length; d++)
            {
                for (var c = 0; c < clas.Length; c++)
                {
                    var cla = clas[c];
                    for (var w = 0; w < wms.Length; w++)
                    {
                        var wm = wms[w];
                        for (var p = 0; p < pas.Length; p++)
                        {
                            var pa = pas[p];

                            var delete = deletes[d];

                            // Level to delete needs to be empty line: (null, null)
                            if (delete == Delete.WM && wm != UnitPercentageValues.Empty) continue;
                            if (delete == Delete.PA && pa != UnitPercentageValues.Empty) continue;

                            // If deleting WM, CLA needs to exist
                            if (delete == Delete.WM && cla == null) continue;

                            // If deleting PA, WM or CLA need to exist
                            if (delete == Delete.PA && cla == null && wm == null) continue;

                            // Always need a non-null value for each property at PA level
                            if (cla?.Percentage == null && wm?.Percentage == null && pa?.Percentage == null) continue;
                            if (cla?.CalculateOver == null && wm?.CalculateOver == null && pa?.CalculateOver == null) continue;

                            var testCase = new UnitPercentageDeleteEmptyOverrideTestCase()
                            {
                                Delete = delete,
                                CLA = cla,
                                WM = wm,
                                PA = pa,
                            };
                            result.Add([testCase]);
                        }
                    }
                }
            }

            return result;

        }
    }
}

public class UnitPercentageDeleteEmptyOverrideTestCase
{
    public required Delete Delete { get; init; }
    public required UnitPercentageValues CLA { get; init; }
    public required UnitPercentageValues WM { get; init; }
    public required UnitPercentageValues PA { get; init; }

    public override string ToString()
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.Append($"Delete = {this.Delete}");
        stringBuilder.Append($", CLA = {this.CLA?.ToString() ?? "null"}");
        stringBuilder.Append($", WM = {this.WM?.ToString() ?? "null"}");
        stringBuilder.Append($", PA = {this.PA?.ToString() ?? "null"}");

        return stringBuilder.ToString();
    }
}

public record UnitPercentageValues(decimal? Percentage, int? CalculateOver)
{
    public static readonly UnitPercentageValues Empty = new(null, null);

    public override string ToString() => $"({this.Percentage?.ToString() ?? "null"}, {this.CalculateOver?.ToString() ?? "null"})";
}

public enum Delete { WM, PA }
