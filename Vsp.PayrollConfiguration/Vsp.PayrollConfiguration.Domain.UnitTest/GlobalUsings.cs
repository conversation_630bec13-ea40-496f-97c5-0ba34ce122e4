global using AutoMapper;
global using FluentAssertions;
global using FluentValidation;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.Logging;
global using Microsoft.VisualStudio.TestTools.UnitTesting;
global using NSubstitute;
global using Vsp.Commands;
global using Vsp.Infrastructure;
global using Vsp.Infrastructure.EntityFilter;
global using Vsp.Infrastructure.Interface;
global using Vsp.PayrollConfiguration.Repository;
global using Vsp.PayrollConfiguration.Repository.Entities;
global using Vsp.PayrollConfiguration.Repository.Entities.Base;
global using Vsp.PayrollConfiguration.Repository.Entities.Loket;
global using Vsp.PayrollConfiguration.Repository.Interfaces;
global using Vsp.UnitTesting;
