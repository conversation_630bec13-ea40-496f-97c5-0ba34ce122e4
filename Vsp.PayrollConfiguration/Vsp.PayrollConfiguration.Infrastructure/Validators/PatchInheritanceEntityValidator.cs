using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Patch;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Infrastructure.Validators;

public class PatchInheritanceEntityValidator<TPatchModel, TEntity, TModelEntity> : AbstractValidator<TPatchModel>
    where TPatchModel : class, IPatchModel
    where TEntity : GeneratedIdEntity, IInheritanceEntity, new()
    where TModelEntity : GeneratedIdEntity, IInheritanceEntity, new()
{
    private readonly ILoketContext loketContext;
    private readonly IMapper mapper;

    public PatchInheritanceEntityValidator(ILoketContext loketContext, IMapper mapper)
    {
        this.loketContext = loketContext;
        this.mapper = mapper;

        this.RuleLevelCascadeMode = CascadeMode.Stop;

        // No default validations yet.
    }
}
