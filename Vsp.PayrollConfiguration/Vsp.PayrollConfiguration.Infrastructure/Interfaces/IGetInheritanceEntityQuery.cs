using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Infrastructure.Interfaces;

public interface IGetInheritanceEntityQuery<TModel, TEntity>
    where TModel : class, new()
    where TEntity : GeneratedIdEntity, IInheritanceEntity, new()
{
    Task<IOperationResult<TModel>> ExecuteAsync(Guid id);
    Task<IListOperationResult<TModel>> ExecuteListAsync(Guid inheritanceLevelId);
}
