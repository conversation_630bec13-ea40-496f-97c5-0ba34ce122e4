using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.PayrollAdministration.Queries;
internal class GetPayrollAdministrationByIdQuery(IGetByParametersQueryDependencies<ILoketContext> dependencies)
    : GetByIdQuery<PayrollAdministrationModel, InheritanceLevel, ILoketContext>(dependencies)
{ }
