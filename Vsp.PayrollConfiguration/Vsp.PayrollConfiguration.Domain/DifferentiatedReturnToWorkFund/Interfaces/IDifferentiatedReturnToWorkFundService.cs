using Vsp.PayrollConfiguration.Domain.DifferentiatedReturnToWorkFund.Models;

namespace Vsp.PayrollConfiguration.Domain.DifferentiatedReturnToWorkFund.Interfaces;

public interface IDifferentiatedReturnToWorkFundService
{
    /// <summary>
    /// Retrieves differentiated return to work funds by payroll administration ID.
    /// </summary>
    /// <param name="yearId">The payroll administration ID.</param>
    /// <returns>The operation result.</returns>
    Task<IListOperationResult<DifferentiatedReturnToWorkFundModel>> GetDifferentiatedReturnToWorkFundsByPayrollAdministrationYearIdAsync(Guid yearId);

    /// <summary>
    /// Creates a new differentiated return to work fund.
    /// </summary>
    /// <param name="payrollAdministrationId">The unique identifier (GUID/UUID) of the associated Payroll Administration.</param>
    /// <param name="postModel">The model containing creation data for the differentiated return to work fund.</param>
    /// <returns>The operation result.</returns>
    Task<IOperationResult<DifferentiatedReturnToWorkFundModel>> PostDifferentiatedReturnToWorkFundByPayrollAdministrationIdAsync(
        Guid payrollAdministrationId,
        DifferentiatedReturnToWorkFundPostModel postModel);

    /// <summary>
    /// Updates a differentiated return to work fund by its ID.
    /// </summary>
    /// <param name="differentiatedReturnToWorkFundId">The unique identifier (GUID/UUID) of the differentiated return to work fund.</param>
    /// <param name="putModel">The model containing updated data for the differentiated return to work fund.</param>
    /// <returns>The operation result.</returns>
    Task<IOperationResult<DifferentiatedReturnToWorkFundModel>> PutDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdAsync(Guid differentiatedReturnToWorkFundId, DifferentiatedReturnToWorkFundPutModel putModel);

    /// <summary>
    /// Deletes a differentiated return to work fund by its ID.
    /// </summary>
    /// <param name="differentiatedReturnToWorkFundId"></param>
    /// <returns>The operation result.</returns>
    Task<IOperationResult<NoResult>> DeleteDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdAsync(Guid differentiatedReturnToWorkFundId);
}