using InheritanceLevel = Vsp.AuthorizationService.Internal.ApiProtocol.Authorization.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.DifferentiatedReturnToWorkFund.Authorizations;

public class DifferentiatedReturnToWorkFundAuthorizationModel
{
    public Guid DifferentiatedReturnToWorkFundId { get; set; }
}

public class DifferentiatedReturnToWorkFundAuthorization : AuthorizeLoketBase<DifferentiatedReturnToWorkFundAuthorizationModel>
{
    public override Task<(ResourceType ResourceType, Guid EntityId)> AuthorizeLoketEntity(ICurrentContext currentContext, DifferentiatedReturnToWorkFundAuthorizationModel authorizationObject) =>
        Task.FromResult((ResourceType.WgaGedifferentieerd, authorizationObject.DifferentiatedReturnToWorkFundId));

    public override Task<InheritanceLevel?> AuthorizeLoketInheritanceLevel(ICurrentContext currentContext, DifferentiatedReturnToWorkFundAuthorizationModel authorizationObject) =>
        Task.FromResult((InheritanceLevel?)InheritanceLevel.PayrollAdministration);
}
