using Vsp.PayrollConfiguration.Domain.UnitPercentage.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Infrastructure.Validators;
using Vsp.PayrollConfiguration.Repository.Enums;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.UnitPercentage.Validators;

internal class InsertUnitPercentageValidator : AbstractValidator<UnitPercentagePostModel>
{
    private readonly ILoketContext loketContext;
    private readonly IMapper mapper;

    public InsertUnitPercentageValidator(ILoketContext loketContext, IMapper mapper)
    {
        this.loketContext = loketContext;
        this.mapper = mapper;

        this.ClassLevelCascadeMode = CascadeMode.Stop;

        Include(new InsertInheritanceEntityValidator<UnitPercentagePostModel, Repository.Entities.UnitPercentage, ModelUnitPercentage>(this.loketContext, this.mapper));

        RuleFor(obj => obj)
            .MustAsync(BeValidPayrollComponentAsync)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_UnitPercentage_PayrollComponent_Invalid)
            .WithMessage(
                "payrollComponent.key is invalid for unit percentage: it doesn't exist on current inheritance level or doesn't have category 6 (unit).");
    }

    private async Task<bool> BeValidPayrollComponentAsync(UnitPercentagePostModel postModel, CancellationToken token) =>
        await this.loketContext.Set<Component>()
            .AnyAsync(c => c.ComponentId == postModel.PayrollComponent.Key &&
                           c.YearId == postModel.Year &&
                           c.Year.InheritanceLevel.Id == postModel.InheritanceLevelId &&
                           c.Category == (int)Category.Units, token);
}