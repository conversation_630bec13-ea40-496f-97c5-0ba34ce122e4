using Vsp.PayrollConfiguration.Domain.UnitPercentage.Models;
using Vsp.PayrollConfiguration.Infrastructure.Queries;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.UnitPercentage.Queries;

internal class GetUnitPercentageQuery(IFilteredQueryDependencies<ILoketContext> dependencies)
    : GetInheritanceEntityQuery<UnitPercentageModel, Repository.Entities.UnitPercentage>(dependencies)
{ }
