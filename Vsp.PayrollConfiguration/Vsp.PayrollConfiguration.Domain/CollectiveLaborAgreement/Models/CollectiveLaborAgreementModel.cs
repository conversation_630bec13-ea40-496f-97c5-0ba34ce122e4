namespace Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Models;

/// <summary>
/// Details of a collective labor agreement.
/// </summary>
public class CollectiveLaborAgreementModel
{
    /// <summary>
    /// The unique identifier of a collective labor agreement (GUID/UUID).
    /// </summary>
    /// <example>123e4567-e89b-12d3-a456-************</example>
    public Guid Id { get; set; }

    /// <summary>
    /// Name of the collective labor agreement.
    /// </summary>
    /// <example>18 Bloemendetailhandel</example>
    [JsonProperty(Required = Required.DisallowNull)]
    public string Description { get; set; } = null!;

    /// <summary>
    /// Additional comment.
    /// </summary>
    /// <example>No comments</example>
    public string? Comment { get; set; }
}
