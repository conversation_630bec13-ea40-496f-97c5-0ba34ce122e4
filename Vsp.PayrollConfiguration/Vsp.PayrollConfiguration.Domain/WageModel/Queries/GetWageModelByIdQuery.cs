using Vsp.PayrollConfiguration.Domain.WageModel.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.WageModel.Queries;

internal class GetWageModelByIdQuery(IGetByParametersQueryDependencies<ILoketContext> dependencies)
    : GetByIdQuery<WageModelModel, InheritanceLevel, ILoketContext>(dependencies)
{ }
