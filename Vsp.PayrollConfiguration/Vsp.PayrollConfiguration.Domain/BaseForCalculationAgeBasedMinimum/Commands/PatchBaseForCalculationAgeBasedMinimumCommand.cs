using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Models;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Infrastructure.Commands;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Commands;

public class PatchBaseForCalculationAgeBasedMinimumCommand(
    IBaseCommandDependencies<ILoketContext> dependencies,
    IGetInheritanceEntityQuery<BaseForCalculationAgeBasedMinimumModel, Repository.Entities.BaseForCalculationAgeBasedMinimum> query)
    : PatchInheritanceEntityCommand<BaseForCalculationAgeBasedMinimumPatchModel, BaseForCalculationAgeBasedMinimumModel, Repository.Entities.BaseForCalculationAgeBasedMinimum, ModelBaseForCalculationAgeBasedMinimum>(dependencies, query)
{
    protected override bool UpdateOnly => false;

    private static readonly IEnumerable<(PropertyInfo Entity, PropertyInfo ModelEntity)> properties =
    [
        (typeof(Repository.Entities.BaseForCalculationAgeBasedMinimum).GetProperty(nameof(Repository.Entities.BaseForCalculationAgeBasedMinimum.BaseOrigin))!, typeof(ModelBaseForCalculationAgeBasedMinimum).GetProperty(nameof(ModelBaseForCalculationAgeBasedMinimum.Origin))!),

    ];

    protected override IEnumerable<(PropertyInfo Entity, PropertyInfo ModelEntity)> Properties => properties;
}