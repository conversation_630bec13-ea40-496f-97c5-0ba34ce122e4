using Vsp.PayrollConfiguration.Domain.Shared.Models;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Models;

public class BaseForCalculationAgeBasedMinimumModel
{
    public Guid Id { get; set; }

    public InheritanceLevelModel InheritanceLevel { get; set; } = null!;
    
    public KeyModel BaseForCalculation { get; set; } = null!;

    public int Age { get; set; }

    public int Year { get; set; }

    public PayrollPeriodModel StartPayrollPeriod { get; set; } = null!;
    
    [JsonConverter(typeof(RoundingDecimalConverter), 2)]
    [Range(0, 999999.99, ErrorMessage = "'{0}' must be between {1} and {2}")]
    public decimal Minimum { get; set; }
    
    public BaseForCalculationAgeBasedMinimumDefinedAtLevelModel DefinedAtLevel { get; set; } = null!;
    
    public class BaseForCalculationAgeBasedMinimumDefinedAtLevelModel
    {
        public InheritanceLevelTypeModel Id { get; set; } = null!;
        
        public InheritanceLevelTypeModel Age { get; set; } = null!;
        
        public InheritanceLevelTypeModel StartPayrollPeriod { get; set; } = null!;
        
        public InheritanceLevelTypeModel Minimum { get; set; } = null!;
    }
}