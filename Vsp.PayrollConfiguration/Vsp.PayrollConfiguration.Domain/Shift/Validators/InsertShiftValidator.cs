using Vsp.PayrollConfiguration.Domain.Shift.Models;
using Vsp.PayrollConfiguration.Infrastructure.Validators;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Shift.Validators;

internal class InsertShiftValidator : AbstractValidator<ShiftPostModel>
{
    private readonly ILoketContext loketContext;
    private readonly IMapper mapper;

    public InsertShiftValidator(ILoketContext loketContext, IMapper mapper)
    {
        this.loketContext = loketContext;
        this.mapper = mapper;

        Include(new InsertInheritanceEntityValidator<ShiftPostModel, Repository.Entities.Shift, ModelShift>(this.loketContext, this.mapper));
    }
}
