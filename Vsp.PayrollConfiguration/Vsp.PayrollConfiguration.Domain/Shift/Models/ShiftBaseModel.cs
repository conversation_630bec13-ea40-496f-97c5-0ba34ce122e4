namespace Vsp.PayrollConfiguration.Domain.Shift.Models;

/// <summary>
/// Details of a shift.
/// </summary>
public class ShiftBaseModel
{
    /// <summary>
    /// The full time hours for an employment that is linked to this shift.
    /// </summary>
    /// <example>38.55</example>
    [Required]
    [Range(0.01, 99.99, ErrorMessage = "{0} must have a value between '{1}' and '{2}'.")]
    [RegularExpression("^-?\\d*([.,]\\d{1,2})?$", ErrorMessage = "{0} must have at most 2 decimals.")]
    [JsonConverter(typeof(RoundingDecimalConverter), 2)]
    public decimal? FullTimeHoursPerWeek { get; set; } = null!;

    /// <summary>
    /// The bonus percentage used in wage calculations for the hours made in this shift.
    /// </summary>
    /// <example>11.995</example>
    [Required]
    [Range(0.000, 100.000, ErrorMessage = "{0} must have a value between '{1}' and '{2}'.")]
    [RegularExpression("^-?\\d*([.,]\\d{1,3})?$", ErrorMessage = "{0} must have at most 3 decimals.")]
    [JsonConverter(typeof(RoundingDecimalConverter), 3)]
    public decimal? BonusPercentage { get; set; } = null!;
}