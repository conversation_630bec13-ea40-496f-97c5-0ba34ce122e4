using Vsp.PayrollConfiguration.Domain.Shift.Models;
using Vsp.PayrollConfiguration.Infrastructure.Commands;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Shift.Commands;

internal class PatchShiftCommand(
    IBaseCommandDependencies<ILoketContext> dependencies,
    IGetInheritanceEntityQuery<ShiftModel, Repository.Entities.Shift> query)
    : PatchInheritanceEntityCommand<ShiftPatchModel, ShiftModel, Repository.Entities.Shift, ModelShift>(dependencies, query)
{
    protected override bool UpdateOnly => false;

    private static readonly IEnumerable<(PropertyInfo Entity, PropertyInfo ModelEntity)> properties =
    [
        ( typeof(Repository.Entities.Shift).GetProperty(nameof(Repository.Entities.Shift.FullTimeHoursPerWeek))!, typeof(ModelShift).GetProperty(nameof(ModelShift.FullTimeHoursPerWeek))! ),
        ( typeof(Repository.Entities.Shift).GetProperty(nameof(Repository.Entities.Shift.BonusPercentage))!, typeof(ModelShift).GetProperty(nameof(ModelShift.BonusPercentage))! ),
    ];

    protected override IEnumerable<(PropertyInfo Entity, PropertyInfo ModelEntity)> Properties => properties;
}