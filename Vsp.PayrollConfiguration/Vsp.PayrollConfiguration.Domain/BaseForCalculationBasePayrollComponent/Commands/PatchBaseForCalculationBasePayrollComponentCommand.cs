using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Infrastructure.Commands;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Commands;

public class PatchBaseForCalculationBasePayrollComponentCommand(
    IBaseCommandDependencies<ILoketContext> dependencies,
    IGetInheritanceEntityQuery<BaseForCalculationBasePayrollComponentModel, Repository.Entities.BaseForCalculationBasePayrollComponent> query)
    : PatchInheritanceEntityCommand<BaseForCalculationBasePayrollComponentPatchModel, BaseForCalculationBasePayrollComponentModel, Repository.Entities.BaseForCalculationBasePayrollComponent, ModelBaseForCalculationBasePayrollComponent>(dependencies, query)
{
    protected override bool UpdateOnly => false;

    private static readonly IEnumerable<(PropertyInfo Entity, PropertyInfo ModelEntity)> properties =
    [
        (typeof(Repository.Entities.BaseForCalculationBasePayrollComponent).GetProperty(nameof(Repository.Entities.BaseForCalculationBasePayrollComponent.BaseOrigin))!, typeof(ModelBaseForCalculationBasePayrollComponent).GetProperty(nameof(ModelBaseForCalculationBasePayrollComponent.Origin))!),

    ];

    protected override IEnumerable<(PropertyInfo Entity, PropertyInfo ModelEntity)> Properties => properties;
}