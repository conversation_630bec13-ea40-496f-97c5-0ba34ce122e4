using Vsp.PayrollConfiguration.Domain.PayrollComponent.Authorizations;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Commands;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Interfaces;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Queries;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Services;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Validators;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponent;

public class ServiceRegistrationBundle : IServiceRegistrationBundle
{
    public void RegisterBundle(IServiceCollection services)
    {
        services.AddScoped<IAuthorizeApiProtocol<PayrollComponentAuthorizationModel>, PayrollComponentAuthorization>();

        services.AddScoped<IPayrollComponentService, PayrollComponentService>();

        services.AddScoped<IFilteredQuery<PayrollComponentModel, Component, Repository.Entities.Year, ILoketContext>, GetPayrollComponentsQuery>();
        services.AddScoped<IFilteredQuery<PayrollComponentMinimizedModel, Component, Repository.Entities.Year, ILoketContext>, GetPayrollComponentsMinimizedQuery>();
        services.AddScoped<IFilteredQuery<PayrollComponentMinimizedModel, ComponentGeneral, Repository.Entities.Year, ILoketContext>, GetAvailablePayrollComponentsQuery>();

        services.AddScoped<IGetInheritanceEntityQuery<PayrollComponentModel, Component>, GetPayrollComponentQuery>();
        services.AddScoped<IInsertInheritanceEntityCommand<PayrollComponentPostModel, PayrollComponentModel, Component, ModelComponent>, InsertPayrollComponentCommand>();
        services.AddScoped<IPatchInheritanceEntityCommand<PayrollComponentPatchModel, PayrollComponentModel, Component, ModelComponent>, PatchPayrollComponentCommand>();
        services.AddScoped<IDeleteInheritanceEntityCommand<ModelComponent>, DeletePayrollComponentCommand>();

        services.AddScoped<GetAvailablePayrollComponentsQuery>();

        services.AddScoped<IValidator<PayrollComponentPostModel>, PostPayrollComponentValidator>();
        services.AddScoped<IValidator<PayrollComponentPatchModel>, PatchPayrollComponentValidator>();
        services.AddScoped<IValidator<ModelComponent>, DeletePayrollComponentValidator>();
    }
}