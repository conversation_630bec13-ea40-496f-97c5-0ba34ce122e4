namespace Vsp.PayrollConfiguration.Domain.Shared.Authorizations;

public class UserAuthorizationModel
{
}

public class UserAuthorization : AuthorizeLoketBase<UserAuthorizationModel>
{
    public override Task<(ResourceType ResourceType, Guid EntityId)> AuthorizeLoketEntity(ICurrentContext currentContext, UserAuthorizationModel authorizationObject) =>
        Task.FromResult((ResourceType.User, currentContext.UserId));
}