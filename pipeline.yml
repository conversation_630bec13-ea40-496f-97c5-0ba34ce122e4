name: $(date:yyyyMMdd)$(rev:.r)-$(SourceBranchName)

trigger:
  branches:
    include:
      - dev
      - main

pool:
  name: 'K8SDockerBuild'
  vmImage: ubuntu-latest

resources:
  repositories:
  - repository: pipeline-templates
    type: git
    name: 'Scrum/pipeline-templates'
    ref: refs/heads/dev
 
variables:
  - template: templates/variables-template.yml@pipeline-templates

stages:

## Build, Test and Publish MicroService ##
- template: Vsp.PayrollConfiguration/Vsp.PayrollConfiguration/CICD/microservice.yml
  parameters:
    SolutionRootFolder: 'Vsp.PayrollConfiguration'
    ProjectFolder: 'Vsp.PayrollConfiguration'
    TestDependsOn: ['BuildMicroService']
